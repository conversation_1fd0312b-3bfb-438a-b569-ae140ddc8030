using Microsoft.Extensions.Options;

namespace RealEstate.InternalAPI.Middleware
{
    /// <summary>
    /// Middleware to authenticate requests using API Key from X-Internal-Api-Key header
    /// </summary>
    public class ApiKeyAuthMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ApiKeyAuthMiddleware> _logger;
        private readonly InternalApiSettings _settings;
        private const string API_KEY_HEADER_NAME = "X-Internal-Api-Key";

        public ApiKeyAuthMiddleware(
            RequestDelegate next, 
            ILogger<ApiKeyAuthMiddleware> logger,
            IOptions<InternalApiSettings> settings)
        {
            _next = next;
            _logger = logger;
            _settings = settings.Value;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only apply API key authentication to jobs endpoints
            if (!context.Request.Path.StartsWithSegments("/api/internal/jobs"))
            {
                await _next(context);
                return;
            }

            if (!context.Request.Headers.TryGetValue(API_KEY_HEADER_NAME, out var extractedApiKey))
            {
                _logger.LogWarning("API Key missing from request to {Path} from {RemoteIpAddress}", 
                    context.Request.Path, context.Connection.RemoteIpAddress);
                
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("API Key is missing");
                return;
            }

            if (string.IsNullOrWhiteSpace(_settings.SecretKey))
            {
                _logger.LogError("Internal API Secret Key is not configured");
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync("Internal server configuration error");
                return;
            }

            if (!_settings.SecretKey.Equals(extractedApiKey))
            {
                _logger.LogWarning("Invalid API Key provided for request to {Path} from {RemoteIpAddress}", 
                    context.Request.Path, context.Connection.RemoteIpAddress);
                
                context.Response.StatusCode = 403;
                await context.Response.WriteAsync("Invalid API Key");
                return;
            }

            _logger.LogDebug("API Key authentication successful for request to {Path}", context.Request.Path);
            await _next(context);
        }
    }

    /// <summary>
    /// Configuration settings for Internal API
    /// </summary>
    public class InternalApiSettings
    {
        public string SecretKey { get; set; } = string.Empty;
    }

    /// <summary>
    /// Extension method to register the ApiKeyAuthMiddleware
    /// </summary>
    public static class ApiKeyAuthMiddlewareExtensions
    {
        public static IApplicationBuilder UseApiKeyAuth(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ApiKeyAuthMiddleware>();
        }
    }
}
