{"JWT": {"Key": "3b738dea24059f653f6893317404f3ce9d90daa8aa9412eafb061ce88058545b013297f9821a54cec1fa4132622e784f22e44c75bdd781cafd045169b216334b", "Issuer": "https://localhost:7057/", "Audience": "https://localhost:3000/", "AccessTokenExpiration": 15, "RefreshTokenExpiration": 7}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.EntityFrameworkCore": "Information", "EFCore": "Debug", "RealEstate.API.Controllers": "Information"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "Seq": {"ServerUrl": "http://localhost:5341", "ApiKey": "yezhome_api_key"}, "AllowedHosts": "*", "ConnectionStrings": {"YezHomeConnection": "Host=localhost;Port=5432;Database=yezhome;Username=postgres;Password=******;SSL Mode=VerifyFull;Channel Binding=Require;"}, "Cors": {"AllowedOrigins": ["https://localhost:3000", "https://another-allowed-origin.com"]}, "AnalysisFromFrontEnd": {"AllowedOrigins": ["https://localhost:3000"]}, "AWS": {"Region": "us-east-1", "SES": {"AccessKey": "SES_ACCESS_KEY", "SecretKey": "SES_SECRET_KEY", "Region": "us-east-1", "FromEmail": "<EMAIL>", "FromName": "YEZHOME"}, "S3": {"AccessKey": "S3_ACCESS_KEY", "SecretKey": "S3_SECRET_KEY", "Region": "us-east-1", "BucketName": "yezhome-media"}}, "Storage": {"Provider": "Local", "Local": {}, "S3": {"BucketName": "yezhome-media", "Region": "us-east-1"}}, "NextJsBaseUrl": "https://localhost:3000", "InternalEmail": {"NoReply": "<EMAIL>", "Support": "<EMAIL>", "ReivewPost": "<EMAIL>"}, "HealthChecksUI": {"HealthChecks": [{"Name": "YezHome API", "Uri": "http://localhost:8080/healthz"}], "EvaluationTimeInSeconds": 86400, "MinimumSecondsBetweenFailureNotifications": 86400}, "VietQR": {"IsEnabled": false, "IsProduction": false, "SecretKey": "vietqr_secret_key_256_bit_for_payment_gateway_authentication_and_signature_validation_change_this_in_production", "Username": "yezhome_vietqr_user", "Password": "yezhome_vietqr_password_change_this_in_production", "Issuer": "YezHome-VietQR-Gateway", "Audience": "VietQR-Client", "TokenExpirationMinutes": 5, "BankAccount": "**********", "BankCode": "970422", "MerchantName": "YEZHOME", "CallbackUrl": "https://localhost:7057/vietqr/transaction-sync", "ReturnUrl": "https://localhost:3000/payment/success", "CancelUrl": "https://localhost:3000/payment/cancel", "Api": {"BaseUrlDev": "https://dev.vietqr.org", "BaseUrlProd": "https://api.vietqr.org", "TimeoutSeconds": 30, "RetryAttempts": 3}}}