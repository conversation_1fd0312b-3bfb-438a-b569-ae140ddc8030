using Microsoft.AspNetCore.Authorization;

namespace RealEstate.API.CustomAuthorizationPolicy
{
    /// <summary>
    /// Authorization requirement for VietQR token validation.
    /// Ensures that only VietQR-issued tokens can access VietQR endpoints.
    /// </summary>
    public class VietQRTokenRequirement : IAuthorizationRequirement
    {
        /// <summary>
        /// The allowed audience for VietQR tokens
        /// </summary>
        public string AllowedAudience { get; }

        /// <summary>
        /// The allowed issuer for VietQR tokens
        /// </summary>
        public string AllowedIssuer { get; }

        public VietQRTokenRequirement(string allowedIssuer, string allowedAudience)
        {
            AllowedIssuer = allowedIssuer ?? throw new ArgumentNullException(nameof(allowedIssuer));
            AllowedAudience = allowedAudience ?? throw new ArgumentNullException(nameof(allowedAudience));
        }
    }
}
