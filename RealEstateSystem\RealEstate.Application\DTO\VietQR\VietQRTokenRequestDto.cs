using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO.VietQR
{
    /// <summary>
    /// Request DTO for VietQR token generation endpoint
    /// This represents the Basic Auth credentials that VietQR will send
    /// </summary>
    public class VietQRTokenRequestDto
    {
        [Required]
        public string Username { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response DTO for VietQR token generation endpoint
    /// This matches the expected response format from VietQR documentation
    /// </summary>
    public class VietQRTokenResponseDto
    {
        public string AccessToken { get; set; } = string.Empty;
        public string TokenType { get; set; } = "Bearer";
        public int ExpiresIn { get; set; }
    }

    /// <summary>
    /// Error response DTO for VietQR token generation failures
    /// </summary>
    public class VietQRTokenErrorResponseDto
    {
        public string Status { get; set; } = "FAILED";
        public string Message { get; set; } = string.Empty;
    }
}
