using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO.VietQR;
using RealEstate.Application.Interfaces;
using System.Text;

namespace RealEstate.API.Controllers
{
    /// <summary>
    /// Controller for VietQR payment gateway integration.
    /// Provides endpoints that VietQR will call for token generation and transaction synchronization.
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    public class VietQRController : BaseController
    {
        private readonly IVietQRTokenService _vietQRTokenService;
        private readonly IVietQRPaymentGatewayService _vietQRPaymentService;
        private readonly ILogger<VietQRController> _logger;

        public VietQRController(
            IVietQRTokenService vietQRTokenService,
            IVietQRPaymentGatewayService vietQRPaymentService,
            ILogger<VietQRController> logger)
        {
            _vietQRTokenService = vietQRTokenService ?? throw new ArgumentNullException(nameof(vietQRTokenService));
            _vietQRPaymentService = vietQRPaymentService ?? throw new ArgumentNullException(nameof(vietQRPaymentService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets the client IP address from the request
        /// </summary>
        private string? GetClientIpAddress()
        {
            return HttpContext.Connection.RemoteIpAddress?.ToString();
        }

        /// <summary>
        /// Token generation endpoint for VietQR.
        /// VietQR calls this endpoint to get Bearer tokens for authentication.
        /// </summary>
        /// <returns>Bearer token response or error</returns>
        [HttpPost("token_generate")]
        [AllowAnonymous]
        public async Task<IActionResult> GenerateToken()
        {
            var clientIp = GetClientIpAddress();
            try
            {
                _logger.LogInformation("VietQR token generation request received from IP: {RemoteIpAddress}", clientIp);

                // Extract Basic Auth credentials from Authorization header
                var authHeader = Request.Headers.Authorization.FirstOrDefault();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Basic "))
                {
                    _logger.LogWarning("VietQR token generation failed: Missing or invalid Authorization header");
                    return BadRequest(new VietQRTokenErrorResponseDto
                    {
                        Status = "FAILED",
                        Message = "Authorization header is missing or invalid"
                    });
                }

                try
                {
                    // Decode Basic Auth credentials
                    var base64Credentials = authHeader.Substring("Basic ".Length).Trim();
                    var credentials = Encoding.UTF8.GetString(Convert.FromBase64String(base64Credentials));
                    var credentialParts = credentials.Split(':', 2);

                    if (credentialParts.Length != 2)
                    {
                        _logger.LogWarning("VietQR token generation failed: Invalid Authorization header format");
                        return BadRequest(new VietQRTokenErrorResponseDto
                        {
                            Status = "FAILED",
                            Message = "Invalid Authorization header format"
                        });
                    }

                    var username = credentialParts[0];
                    var password = credentialParts[1];

                    _logger.LogInformation("VietQR token generation attempt for username: {Username}", username);

                    // Validate credentials
                    var credentialValidation = await _vietQRTokenService.ValidateCredentialsAsync(username, password);
                    if (!credentialValidation.IsSuccess || !credentialValidation.Value)
                    {
                        _logger.LogWarning("VietQR token generation failed: Invalid credentials for username: {Username}", username);
                        return Unauthorized(new VietQRTokenErrorResponseDto
                        {
                            Status = "FAILED",
                            Message = "Invalid credentials"
                        });
                    }

                    // Generate token
                    var tokenResult = await _vietQRTokenService.GenerateTokenAsync(username);
                    if (!tokenResult.IsSuccess)
                    {
                        _logger.LogError("VietQR token generation failed for username: {Username}. Error: {Error}", 
                            username, tokenResult.ErrorMessage);
                        return StatusCode(500, new VietQRTokenErrorResponseDto
                        {
                            Status = "FAILED",
                            Message = "Token generation failed"
                        });
                    }

                    var response = new VietQRTokenResponseDto
                    {
                        AccessToken = tokenResult.Value.AccessToken,
                        TokenType = tokenResult.Value.TokenType,
                        ExpiresIn = tokenResult.Value.ExpiresIn
                    };

                    _logger.LogInformation("VietQR token generated successfully for username: {Username}", username);
                    return Ok(response);
                }
                catch (FormatException ex)
                {
                    _logger.LogWarning(ex, "VietQR token generation failed: Invalid Base64 encoding in Authorization header");
                    return BadRequest(new VietQRTokenErrorResponseDto
                    {
                        Status = "FAILED",
                        Message = "Invalid Authorization header encoding"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during VietQR token generation");
                return StatusCode(500, new VietQRTokenErrorResponseDto
                {
                    Status = "FAILED",
                    Message = "Internal server error"
                });
            }
        }

        /// <summary>
        /// Transaction synchronization endpoint for VietQR.
        /// VietQR calls this endpoint to notify about completed transactions.
        /// </summary>
        /// <param name="transactionData">Transaction details from VietQR</param>
        /// <returns>Transaction processing result</returns>
        [HttpPost("transaction-sync")]
        [Authorize(Policy = "VietQRTokenOnly", AuthenticationSchemes = "VietQRBearer")]
        public async Task<IActionResult> TransactionSync([FromBody] VietQRTransactionSyncRequestDto transactionData)
        {
            var clientIp = GetClientIpAddress();
            try
            {
                _logger.LogInformation("VietQR transaction sync received from IP: {RemoteIpAddress}. TransactionId: {TransactionId}, OrderId: {OrderId}, Amount: {Amount}",
                    clientIp, transactionData?.TransactionId, transactionData?.OrderId, transactionData?.Amount);

                if (transactionData == null)
                {
                    _logger.LogWarning("VietQR transaction sync failed: Request body is null or empty");
                    return BadRequest(new VietQRTransactionSyncErrorResponseDto
                    {
                        Error = true,
                        ErrorReason = "INVALID_REQUEST_BODY",
                        ToastMessage = "Request body is required",
                        Object = null
                    });
                }

                // Token validation is now handled by the VietQRTokenOnly authorization policy
                // No need for manual token validation here

                // Process the transaction
                var processingResult = await _vietQRPaymentService.ProcessTransactionSyncAsync(transactionData);
                if (!processingResult.IsSuccess)
                {
                    _logger.LogError("VietQR transaction processing failed for TransactionId: {TransactionId}. Error: {Error}",
                        transactionData.TransactionId, processingResult.ErrorMessage);

                    return BadRequest(new VietQRTransactionSyncErrorResponseDto
                    {
                        Error = true,
                        ErrorReason = "TRANSACTION_PROCESSING_FAILED",
                        ToastMessage = processingResult.ErrorMessage ?? "Transaction processing failed",
                        Object = null
                    });
                }

                _logger.LogInformation("VietQR transaction processed successfully. TransactionId: {TransactionId}, RefTransactionId: {RefTransactionId}", 
                    transactionData.TransactionId, processingResult.Value.Object.RefTransactionId);

                return Ok(processingResult.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during VietQR transaction sync for TransactionId: {TransactionId}", 
                    transactionData?.TransactionId);
                
                return StatusCode(500, new VietQRTransactionSyncErrorResponseDto
                {
                    Error = true,
                    ErrorReason = "INTERNAL_SERVER_ERROR",
                    ToastMessage = "Internal server error occurred",
                    Object = null
                });
            }
        }

        /// <summary>
        /// Health check endpoint for VietQR integration
        /// </summary>
        /// <returns>Health status</returns>
        [HttpGet("health")]
        [AllowAnonymous]
        public IActionResult Health()
        {
            _logger.LogDebug("VietQR health check requested from IP: {RemoteIpAddress}", 
                HttpContext.Connection.RemoteIpAddress);

            return Ok(new
            {
                status = "healthy",
                service = "VietQR Payment Gateway",
                timestamp = DateTime.UtcNow,
                version = "1.0.0"
            });
        }

        /// <summary>
        /// Test endpoint for VietQR payment request creation (for internal use)
        /// </summary>
        /// <param name="request">Payment request details</param>
        /// <returns>Payment response</returns>
        [HttpPost("create-payment")]
        [Authorize(Policy = "VietQRTokenOnly", AuthenticationSchemes = "VietQRBearer")] // Require VietQR token authentication
        public async Task<IActionResult> CreatePayment([FromBody] VietQRPaymentRequestDto request)
        {
            try
            {
                _logger.LogInformation("VietQR payment creation requested by user. OrderId: {OrderId}, Amount: {Amount}",
                    request.OrderId, request.Amount);

                var result = await _vietQRPaymentService.CreatePaymentRequestAsync(request);

                if (!result.IsSuccess)
                {
                    _logger.LogWarning("VietQR payment creation failed for OrderId: {OrderId}. Error: {Error}",
                        request.OrderId, result.ErrorMessage);
                    return BadRequest(new { error = result.ErrorMessage });
                }

                _logger.LogInformation("VietQR payment created successfully for OrderId: {OrderId}", request.OrderId);
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during VietQR payment creation for OrderId: {OrderId}",
                    request?.OrderId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Generate VietQR code via VietQR API
        /// </summary>
        /// <param name="request">QR generation request</param>
        /// <returns>QR code response</returns>
        [HttpPost("generate-qr")]
        [Authorize(Policy = "VietQRTokenOnly", AuthenticationSchemes = "VietQRBearer")]
        public async Task<IActionResult> GenerateQRCode([FromBody] VietQRGenerateCodeRequestDto request)
        {
            try
            {
                _logger.LogInformation("VietQR code generation requested. OrderId: {OrderId}, Amount: {Amount}, QrType: {QrType}",
                    request.OrderId, request.Amount, request.QrType);

                var result = await _vietQRPaymentService.GenerateVietQRCodeAsync(request);

                if (!result.IsSuccess)
                {
                    _logger.LogWarning("VietQR code generation failed for OrderId: {OrderId}. Error: {Error}",
                        request.OrderId, result.ErrorMessage);
                    return BadRequest(new { error = result.ErrorMessage });
                }

                _logger.LogInformation("VietQR code generated successfully for OrderId: {OrderId}, TransactionRefId: {TransactionRefId}",
                    request.OrderId, result.Value.TransactionRefId);
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during VietQR code generation for OrderId: {OrderId}",
                    request?.OrderId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Get VietQR API token status
        /// </summary>
        /// <returns>Token status information</returns>
        [HttpGet("token-status")]
        [Authorize(Policy = "VietQRTokenOnly", AuthenticationSchemes = "VietQRBearer")]
        public async Task<IActionResult> GetTokenStatus()
        {
            try
            {
                _logger.LogDebug("VietQR token status requested");

                var tokenResult = await _vietQRTokenService.GetVietQRApiTokenAsync();

                if (!tokenResult.IsSuccess)
                {
                    _logger.LogWarning("Failed to get VietQR API token status: {Error}", tokenResult.ErrorMessage);
                    return BadRequest(new { error = tokenResult.ErrorMessage });
                }

                var response = new
                {
                    isValid = !tokenResult.Value.IsExpired,
                    expiresAt = tokenResult.Value.ExpiresAt,
                    tokenType = tokenResult.Value.TokenType,
                    expiresIn = tokenResult.Value.ExpiresIn
                };

                _logger.LogDebug("VietQR token status retrieved successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during VietQR token status check");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }
    }
}
