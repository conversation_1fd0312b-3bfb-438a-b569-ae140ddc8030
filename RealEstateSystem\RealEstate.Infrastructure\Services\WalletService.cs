﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.DTO.Wallet;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Enums;
using Shared.Results;
using System.Drawing;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Infrastructure.Services
{
    public class WalletService : IWalletService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPaymentGatewayService _paymentGatewayService;
        private readonly INotificationService _notificationService;
        private readonly IMapper _mapper;
        private readonly ILogger<WalletService> _logger;

        public WalletService(IUnitOfWork unitOfWork, IMapper mapper, IPaymentGatewayService paymentGatewayService, INotificationService notificationService, ILogger<WalletService> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _paymentGatewayService = paymentGatewayService;
            _notificationService = notificationService;
            _logger = logger;
        }

        public async Task<Result<TopUpInitiationDto>> TopUpAsync(Guid userId, TopUpWalletDto request)
        {
            if (request.Amount <= 0)
            {
                return Result<TopUpInitiationDto>.Failure("Top-up amount must be greater than zero", ErrorType.Validation);
            }

            if (string.IsNullOrEmpty(request.PaymentMethod))
            {
                return Result<TopUpInitiationDto>.Failure("Payment method invalid", ErrorType.Validation);
            }

            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                return Result<TopUpInitiationDto>.Failure("User not found", ErrorType.NotFound);
            }

            // ... Tạo giao dịch PENDING ...
            var transDate = DateTime.UtcNow;
            var transaction = new WalletTransaction
            {
                UserId = userId,
                Amount = request.Amount,
                Type = TransactionType.DEPOSIT.ToString(),
                Status = TransactionStatus.PENDING.ToString(),
                PaymentMethod = request.PaymentMethod,
                Description = $"Top-up via {request.PaymentMethod}: {request.Amount:N0} at {transDate:dd/MM/yyyy HH:mm:ss}.",
                CreatedAt = transDate,
                TransactionReference = Guid.NewGuid().ToString("N"),
            };

            await _unitOfWork.WalletTransactions.AddAsync(transaction);
            await _unitOfWork.SaveChangesAsync();

            // === c. GỌI CỔNG THANH TOÁN
            // (nếu có nhiều cổng thanh toán thì if else và chia ra thành các service cho từng cổng thanh toán khác nhau)
            // ===
            var gatewayRequest = new GatewayRequest
            {
                Amount = transaction.Amount,
                TransactionReference = transaction.TransactionReference,
                ReturnUrl = "https://your-website.com/payment/result",
                NotifyUrl = "https://your-api.com/wallet/webhook"
            };

            var gatewayResult = await _paymentGatewayService.CreatePaymentRequestAsync(gatewayRequest);

            if (!gatewayResult.IsSuccess)
            {
                // Nếu việc gọi gateway thất bại, cập nhật giao dịch của bạn thành "failed"
                transaction.Status = "failed";
                transaction.FailureReason = gatewayResult.ErrorMessage;
                _unitOfWork.WalletTransactions.Update(transaction);
                await _unitOfWork.SaveChangesAsync();

                return Result<TopUpInitiationDto>.Failure(gatewayResult.ErrorMessage, ErrorType.Internal);
            }

            // === d. Trả về KẾT QUẢ cho Frontend (BƯỚC THAY ĐỔI) ===
            var response = new TopUpInitiationDto
            {
                TransactionId = transaction.Id,
                PaymentUrl = gatewayResult.PaymentUrl
            };


            return Result<TopUpInitiationDto>.Success(response);
        }

        public async Task<Result> HandlePaymentNotificationAsync(PaymentNotificationDto notification)
        {
            // BẮT BUỘC: Xác thực chữ ký (signature) từ cổng thanh toán để đảm bảo request hợp lệ
            // if (!_paymentGateway.VerifySignature(notification))
            // {
            //     return Result.Failure("Invalid signature", ErrorType.Unauthorized);
            // }
            var receivedTime = DateTime.UtcNow;
            _logger.LogInformation("Received payment notification at {Time}: {Notification}", receivedTime, notification);

            var receivedTimeText = receivedTime.ToString("dd/MM/yyyy HH:mm:ss");

            await using var dbTransaction = await _unitOfWork.BeginTransactionAsync();
            try
            {
                var transaction = await _unitOfWork.WalletTransactions.GetQueryable()
                                        .FirstOrDefaultAsync(t => t.TransactionReference == notification.TransactionReference && t.Status == TransactionStatus.PENDING.ToString());

                if (transaction == null)
                {
                    return Result.Failure("Transaction not found or already processed", ErrorType.NotFound);
                }

                // 2. Hàm này chỉ xử lý giao dịch nạp tiền
                if (transaction.Type != TransactionType.DEPOSIT.ToString())
                {
                    return Result.Failure("Invalid transaction type for this operation", ErrorType.Validation);
                }

                if (notification.IsSuccess)
                {
                    // 3. Cập nhật số dư VÍ
                    var wallet = await _unitOfWork.Wallets.GetQueryable().FirstOrDefaultAsync(w => w.UserId == transaction.UserId);
                    if (wallet == null)
                    {
                        await dbTransaction.RollbackAsync();
                        return Result.Failure("User wallet not found", ErrorType.NotFound);
                    }
                    wallet.Balance += transaction.Amount;
                    // Cân nhắc thêm cơ chế Optimistic Concurrency cho `wallet` ở đây
                    _unitOfWork.Wallets.Update(wallet);

                    // 4. Cập nhật trạng thái GIAO DỊCH
                    transaction.Status = TransactionStatus.COMPLETED.ToString();
                    transaction.ExternalPaymentReference = notification.ExternalTransactionId; // Lưu lại mã của cổng thanh toán

                    // 5. Cập nhật in app notification cho người dùng
                    var notificationMessage = $"Nạp tiền thành công: {transaction.Amount:N0} vào ví lúc {receivedTimeText} mã giao dịch {notification.TransactionReference}";
                    var userNotification = new Notification
                    {
                        UserId = transaction.UserId,
                        Title = $"Nạp tiền thành công",
                        Message = notificationMessage,
                        RelatedEntityId = transaction.Id,
                        Category =  NotificationCategory.Finance,
                        Type = NotificationType.WalletTopUpSuccess,
                        IsRead = false,
                        CreatedAt = DateTime.UtcNow
                    };
                    await _unitOfWork.Notifications.AddAsync(userNotification);
                }
                else
                {
                    transaction.Status = TransactionStatus.FAILED.ToString();
                    transaction.FailureReason = notification.FailureReason;

                    // 4. Cập nhật in app notification cho người dùng
                    var notificationMessage = $"Nạp tiền thất bại: {transaction.Amount:N0} vào ví lúc {receivedTimeText}. Lý do: {notification.FailureReason} mã giao dịch {notification.TransactionReference}";
                    var userNotification = new Notification
                    {
                        UserId = transaction.UserId,
                        Title = $"Nạp tiền thất bại",
                        Message = notificationMessage,
                        RelatedEntityId = transaction.Id,
                        Category = NotificationCategory.Finance,
                        Type = NotificationType.WalletTopUpFailed,
                        IsRead = false,
                        CreatedAt = DateTime.UtcNow
                    };
                    await _unitOfWork.Notifications.AddAsync(userNotification);
                }

                transaction.ProcessedAt = DateTime.UtcNow;
                _unitOfWork.WalletTransactions.Update(transaction);

                // 5. Lưu tất cả thay đổi và commit
                await _unitOfWork.SaveChangesAsync();
                await dbTransaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await dbTransaction.RollbackAsync();
                // Log lỗi ex
                return Result.Failure("An internal error occurred", ErrorType.Internal);
            }

            try
            {
                // Gửi notification ra ngoài (email, push notification, v.v.)
                var transaction = await _unitOfWork.WalletTransactions.GetQueryable()
                                        .FirstOrDefaultAsync(t => t.TransactionReference == notification.TransactionReference);

                if (transaction == null)
                {
                    return Result.Failure("Transaction not found", ErrorType.NotFound);
                }

                var user = await _unitOfWork.AppUsers.GetByIdAsync(transaction.UserId);
                var wallet = await _unitOfWork.Wallets.GetQueryable().FirstOrDefaultAsync(w => w.UserId == transaction.UserId);

                var emailType = notification.IsSuccess ? NotificationType.WalletTopUpSuccess.ToString() : NotificationType.WalletTopUpFailed.ToString();
                var notificationTypeText = NotificationMapper.GetNotificationTypeText(notification.IsSuccess ? NotificationType.WalletTopUpSuccess : NotificationType.WalletTopUpFailed);

                var request = new NotificationRequest
                {
                    TargetChannels = NotificationChannel.Email | NotificationChannel.Push,
                    RecipientId = transaction.Id,
                    RecipientEmail = user.Email,
                    EmailType = emailType,
                    Title = notificationTypeText,
                    Data = new Dictionary<string, string>
                    {
                        { "user_name", user.FullName },
                        { "transaction_id", notification.TransactionReference },
                        { "amount_top_up", transaction.Amount.ToString("") },
                        { "new_balance", wallet.Balance.ToString("") },
                        { "transaction_time", receivedTimeText },
                        { "wallet_link", "/user/wallet" },
                        { "failure_reason", notification.IsSuccess ? "Thành công" : notification.FailureReason },
                    }
                };

                // Gửi đi
                await _notificationService.SendAsync(request);
            }
            catch (Exception ex)
            {
                // Ghi log cảnh báo: Giao dịch thành công nhưng gửi notification bên ngoài thất bại.
                // Việc này không nên ảnh hưởng đến kết quả thành công của giao dịch.
                _logger.LogWarning(ex, "Gửi notification bên ngoài thất bại cho giao dịch thành công.");
            }

            return Result.Success();
        }

        public async Task<Result<WalletTransactionDto>> SpendAsync(Guid userId, SpendWalletDto request)
        {
            // 1. Lấy ví và kiểm tra số dư, và số tiền sẽ xài
            if (request.Amount <= 0)
            {
                return Result<WalletTransactionDto>.Failure("Payment amount must be greater than zero", ErrorType.Validation);
            }

            var wallet = await _unitOfWork.Wallets.GetQueryable().FirstOrDefaultAsync(w => w.UserId == userId);
            if (wallet == null || wallet.Balance < request.Amount)
            {
                return Result<WalletTransactionDto>.Failure("Insufficient funds in wallet", ErrorType.Validation);
            }

            await using var transaction = await _unitOfWork.BeginTransactionAsync();
            try
            {
                wallet.Balance -= request.Amount;
                _unitOfWork.Wallets.Update(wallet);

                // 3. TẠO MỚI một giao dịch với trạng thái "completed" ngay lập tức
                var newTransaction = new WalletTransaction
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    Amount = request.Amount,
                    Type = request.PaymentType ?? TransactionType.SPEND.ToString(),
                    Description = request.Description ?? "Trừ tiền trong ví",
                    Status = TransactionStatus.COMPLETED.ToString(),
                    CreatedAt = DateTime.UtcNow,
                    ProcessedAt = DateTime.UtcNow,
                    TransactionReference = Guid.NewGuid().ToString("N")
                };
                await _unitOfWork.WalletTransactions.AddAsync(newTransaction);

                await _unitOfWork.SaveChangesAsync(); // <-- NẾU CÓ XUNG ĐỘT, LỖI SẼ XẢY RA Ở ĐÂY
                await _unitOfWork.CommitTransactionAsync(transaction);

                return Result<WalletTransactionDto>.Success(_mapper.Map<WalletTransactionDto>(newTransaction));
            }
            // THÊM KHỐI CATCH NÀY VÀO
            catch (DbUpdateConcurrencyException ex)
            {
                await _unitOfWork.RollbackTransactionAsync(transaction);
                _logger.LogError(ex, "Failed to update wallet - DbUpdateConcurrencyException");

                // Dữ liệu đã bị thay đổi bởi một tiến trình khác.
                // Cách xử lý tốt nhất là thông báo cho người dùng thử lại.
                return Result<WalletTransactionDto>.Failure(
                    "Your wallet balance was updated by another action. Please try again.",
                    ErrorType.Conflict); // Conflict (409) là mã lỗi phù hợp
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync(transaction);
                // Log lỗi ex
                _logger.LogError(ex, "Failed to update wallet - DbUpdateConcurrencyException");
                return Result<WalletTransactionDto>.Failure("An internal error occurred", ErrorType.Internal);
            }
        }

        public async Task<Result<WalletTransactionDto>> SpendWithoutTransactionAsync(Guid userId, SpendWalletDto request)
        {
            // 1. Lấy ví và kiểm tra số dư, và số tiền sẽ xài
            if (request.Amount <= 0)
            {
                return Result<WalletTransactionDto>.Failure("Payment amount must be greater than zero", ErrorType.Validation);
            }

            var wallet = await _unitOfWork.Wallets.GetQueryable().FirstOrDefaultAsync(w => w.UserId == userId);
            if (wallet == null || wallet.Balance < request.Amount)
            {
                return Result<WalletTransactionDto>.Failure("Insufficient funds in wallet", ErrorType.Validation);
            }

            // Note: This method does NOT manage its own transaction - it expects to be called within an existing transaction
            wallet.Balance -= request.Amount;
            _unitOfWork.Wallets.Update(wallet);

            // 3. TẠO MỚI một giao dịch với trạng thái "completed" ngay lập tức
            var newTransaction = new WalletTransaction
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Amount = request.Amount,
                Type = request.PaymentType ?? TransactionType.SPEND.ToString(),
                Description = request.Description ?? "Trừ tiền trong ví",
                Status = TransactionStatus.COMPLETED.ToString(),
                CreatedAt = DateTime.UtcNow,
                ProcessedAt = DateTime.UtcNow,
                TransactionReference = Guid.NewGuid().ToString("N")
            };
            await _unitOfWork.WalletTransactions.AddAsync(newTransaction);

            // Note: SaveChanges and transaction commit should be handled by the caller
            return Result<WalletTransactionDto>.Success(_mapper.Map<WalletTransactionDto>(newTransaction));
        }

        public async Task<Result<WalletBalanceDto>> GetBalanceAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                return Result<WalletBalanceDto>.Failure($"User with ID {userId} not found", ErrorType.NotFound);
            }

            var wallet = await _unitOfWork.Wallets.GetQueryable().FirstOrDefaultAsync(w => w.UserId == userId);
            if (wallet == null)
            {
                wallet = new Wallet { UserId = userId, Balance = 0, User = user };
                await _unitOfWork.Wallets.AddAsync(wallet);
                await _unitOfWork.SaveChangesAsync();
            }

            var balanceDto = new WalletBalanceDto { Balance = wallet.Balance, TotalSpent = user.TotalSpent };
            return Result<WalletBalanceDto>.Success(balanceDto);
        }

        public async Task<Result<WalletTransactionDto>> GetTransactionByRefIdAsync(string transactionId, Guid userId)
        {
            var transaction = await _unitOfWork.WalletTransactions.GetQueryable()
                .FirstOrDefaultAsync(x => x.TransactionReference.Equals(transactionId) && x.UserId == userId);

            if (transaction == null)
            {
                return Result<WalletTransactionDto>.Failure("Transaction not found or already processed", ErrorType.NotFound);
            }

            return Result<WalletTransactionDto>.Success(_mapper.Map<WalletTransactionDto>(transaction));
        }

        public async Task<Result<IEnumerable<WalletTransactionDto>>> GetTransactionsAsync(Guid userId, int page = 1, int pageSize = 50)
        {
            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(t => t.UserId == userId)
                .OrderByDescending(t => t.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return Result<IEnumerable<WalletTransactionDto>>.Success(_mapper.Map<IEnumerable<WalletTransactionDto>>(transactions));
        }

        public async Task<Result<WalletTransactionDto>> GetTransactionByIdAsync(Guid transactionId, Guid userId)
        {
            var transaction = await _unitOfWork.WalletTransactions.GetQueryable()
                .FirstOrDefaultAsync(t => t.Id == transactionId && t.UserId == userId);

            if (transaction == null)
            {
                return Result<WalletTransactionDto>.Failure("Transaction not found", ErrorType.NotFound);
            }

            return Result<WalletTransactionDto>.Success(_mapper.Map<WalletTransactionDto>(transaction));
        }

        public async Task<Result<IEnumerable<WalletTransactionDto>>> GetPendingTransactionsAsync(int page = 1, int pageSize = 50)
        {
            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(t => t.Status == "pending")
                .OrderByDescending(t => t.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
            return Result<IEnumerable<WalletTransactionDto>>.Success(_mapper.Map<IEnumerable<WalletTransactionDto>>(transactions));
        }

        public async Task<Result<IEnumerable<WalletTransactionDto>>> GetUserPendingTransactionsAsync(Guid userId, int page = 1, int pageSize = 50)
        {
            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(t => t.UserId == userId && t.Status == "pending")
                .OrderByDescending(t => t.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
            return Result<IEnumerable<WalletTransactionDto>>.Success(_mapper.Map<IEnumerable<WalletTransactionDto>>(transactions));
        }

        public async Task<Result<TransactionSearchResultDto>> SearchTransactionsAsync(TransactionSearchCriteriaDto criteria, Guid? userId = null)
        {
            var query = _unitOfWork.WalletTransactions.GetQueryable();

            // Apply user filter if provided
            if (userId.HasValue)
            {
                query = query.Where(t => t.UserId == userId.Value);
            }

            // Apply date filters
            if (criteria.StartDate.HasValue)
            {
                var startDate = criteria.StartDate.Value.Date;
                query = query.Where(t => t.CreatedAt >= startDate);
            }

            if (criteria.EndDate.HasValue)
            {
                var endDate = criteria.EndDate.Value.Date.AddDays(1).AddSeconds(-1); // End of the day
                query = query.Where(t => t.CreatedAt <= endDate);
            }

            // Apply status filter
            if (!string.IsNullOrEmpty(criteria.Status))
            {
                query = query.Where(t => t.Status == criteria.Status);
            }

            // Apply payment method filter
            if (!string.IsNullOrEmpty(criteria.PaymentMethod))
            {
                query = query.Where(t => t.PaymentMethod == criteria.PaymentMethod);
            }

            // Apply transaction type filter
            if (!string.IsNullOrEmpty(criteria.Type))
            {
                query = query.Where(t => t.Type == criteria.Type);
            }

            // Apply amount range filters
            if (criteria.MinAmount.HasValue)
            {
                query = query.Where(t => t.Amount >= criteria.MinAmount.Value);
            }

            if (criteria.MaxAmount.HasValue)
            {
                query = query.Where(t => t.Amount <= criteria.MaxAmount.Value);
            }

            // Apply search term filter (search in description or transaction reference)
            if (!string.IsNullOrEmpty(criteria.SearchTerm))
            {
                var searchTerm = criteria.SearchTerm.ToLower();
                query = query.Where(t =>
                    (t.Description != null && t.Description.ToLower().Contains(searchTerm)) ||
                    (t.TransactionReference != null && t.TransactionReference.ToLower().Contains(searchTerm)));
            }

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Calculate total amount
            var totalAmount = await query.SumAsync(t => t.Amount);

            // Apply pagination
            var transactions = await query
                .OrderByDescending(t => t.CreatedAt)
                .Skip((criteria.Page - 1) * criteria.PageSize)
                .Take(criteria.PageSize)
                .ToListAsync();

            // Calculate total pages
            var totalPages = (int)Math.Ceiling(totalCount / (double)criteria.PageSize);

            var result = new TransactionSearchResultDto
            {
                Transactions = _mapper.Map<IEnumerable<WalletTransactionDto>>(transactions),
                TotalCount = totalCount,
                TotalPages = totalPages,
                CurrentPage = criteria.Page,
                TotalAmount = totalAmount
            };
            return Result<TransactionSearchResultDto>.Success(result);
        }

        public async Task<Result<byte[]>> ExportTransactionsToExcelAsync(TransactionSearchCriteriaDto criteria, Guid? userId = null)
        {
            // First, get the search results (without pagination to export all matching records)
            var searchCriteria = new TransactionSearchCriteriaDto
            {
                StartDate = criteria.StartDate,
                EndDate = criteria.EndDate,
                Status = criteria.Status,
                PaymentMethod = criteria.PaymentMethod,
                Type = criteria.Type,
                MinAmount = criteria.MinAmount,
                MaxAmount = criteria.MaxAmount,
                SearchTerm = criteria.SearchTerm,
                Page = 1,
                PageSize = int.MaxValue // Get all records matching the criteria
            };

            var query = _unitOfWork.WalletTransactions.GetQueryable();

            // Apply user filter if provided
            if (userId.HasValue)
            {
                query = query.Where(t => t.UserId == userId.Value);
            }

            // Apply date filters
            if (criteria.StartDate.HasValue)
            {
                var startDate = criteria.StartDate.Value.Date;
                query = query.Where(t => t.CreatedAt >= startDate);
            }

            if (criteria.EndDate.HasValue)
            {
                var endDate = criteria.EndDate.Value.Date.AddDays(1).AddSeconds(-1); // End of the day
                query = query.Where(t => t.CreatedAt <= endDate);
            }

            // Apply status filter
            if (!string.IsNullOrEmpty(criteria.Status))
            {
                query = query.Where(t => t.Status == criteria.Status);
            }

            // Apply payment method filter
            if (!string.IsNullOrEmpty(criteria.PaymentMethod))
            {
                query = query.Where(t => t.PaymentMethod == criteria.PaymentMethod);
            }

            // Apply transaction type filter
            if (!string.IsNullOrEmpty(criteria.Type))
            {
                query = query.Where(t => t.Type == criteria.Type);
            }

            // Apply amount range filters
            if (criteria.MinAmount.HasValue)
            {
                query = query.Where(t => t.Amount >= criteria.MinAmount.Value);
            }

            if (criteria.MaxAmount.HasValue)
            {
                query = query.Where(t => t.Amount <= criteria.MaxAmount.Value);
            }

            // Apply search term filter (search in description or transaction reference)
            if (!string.IsNullOrEmpty(criteria.SearchTerm))
            {
                var searchTerm = criteria.SearchTerm.ToLower();
                query = query.Where(t =>
                    (t.Description != null && t.Description.ToLower().Contains(searchTerm)) ||
                    (t.TransactionReference != null && t.TransactionReference.ToLower().Contains(searchTerm)));
            }

            // Get transactions with user information
            var transactions = await query
                .OrderByDescending(t => t.CreatedAt)
                .Include(t => t.User)
                .ToListAsync();

            // Create Excel package
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using var package = new ExcelPackage();

            // Add a worksheet
            var worksheet = package.Workbook.Worksheets.Add("Transactions");

            // Add headers
            worksheet.Cells[1, 1].Value = "Transaction ID";
            worksheet.Cells[1, 2].Value = "User";
            worksheet.Cells[1, 3].Value = "Type";
            worksheet.Cells[1, 4].Value = "Amount";
            worksheet.Cells[1, 5].Value = "Status";
            worksheet.Cells[1, 6].Value = "Payment Method";
            worksheet.Cells[1, 7].Value = "Reference";
            worksheet.Cells[1, 8].Value = "Description";
            worksheet.Cells[1, 9].Value = "Created At";
            worksheet.Cells[1, 10].Value = "Processed At";

            // Style the header row
            using (var range = worksheet.Cells[1, 1, 1, 10])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                range.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
            }

            // Add data rows
            for (int i = 0; i < transactions.Count; i++)
            {
                var transaction = transactions[i];
                int row = i + 2; // Start from row 2 (after header)

                worksheet.Cells[row, 1].Value = transaction.Id.ToString();
                worksheet.Cells[row, 2].Value = transaction.User?.FullName ?? "Unknown";
                worksheet.Cells[row, 3].Value = transaction.Type;
                worksheet.Cells[row, 4].Value = transaction.Amount;
                worksheet.Cells[row, 5].Value = transaction.Status;
                worksheet.Cells[row, 6].Value = transaction.PaymentMethod;
                worksheet.Cells[row, 7].Value = transaction.TransactionReference;
                worksheet.Cells[row, 8].Value = transaction.Description;
                worksheet.Cells[row, 9].Value = transaction.CreatedAt;
                worksheet.Cells[row, 10].Value = transaction.ProcessedAt;

                // Format the amount column
                worksheet.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";

                // Format the date columns
                worksheet.Cells[row, 9].Style.Numberformat.Format = "yyyy-mm-dd hh:mm:ss";
                worksheet.Cells[row, 10].Style.Numberformat.Format = "yyyy-mm-dd hh:mm:ss";

                // Highlight rows based on transaction type
                if (transaction.Type == "deposit")
                {
                    worksheet.Cells[row, 3].Style.Font.Color.SetColor(Color.Green);
                    worksheet.Cells[row, 4].Style.Font.Color.SetColor(Color.Green);
                }
                else if (transaction.Type == "spend")
                {
                    worksheet.Cells[row, 3].Style.Font.Color.SetColor(Color.Red);
                    worksheet.Cells[row, 4].Style.Font.Color.SetColor(Color.Red);
                }

                // Highlight rows based on status
                if (transaction.Status == "completed")
                {
                    worksheet.Cells[row, 5].Style.Font.Color.SetColor(Color.Green);
                }
                else if (transaction.Status == "failed")
                {
                    worksheet.Cells[row, 5].Style.Font.Color.SetColor(Color.Red);
                }
                else if (transaction.Status == "pending")
                {
                    worksheet.Cells[row, 5].Style.Font.Color.SetColor(Color.Orange);
                }
            }

            // Add a summary row
            int summaryRow = transactions.Count + 3;
            worksheet.Cells[summaryRow, 1].Value = "Total:";
            worksheet.Cells[summaryRow, 4].Formula = $"SUM(D2:D{transactions.Count + 1})";
            worksheet.Cells[summaryRow, 4].Style.Font.Bold = true;
            worksheet.Cells[summaryRow, 4].Style.Numberformat.Format = "#,##0.00";

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Return the Excel package as a byte array
            return Result<byte[]>.Success(package.GetAsByteArray());
        }

    }
}