using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO.VietQR
{
    /// <summary>
    /// Request DTO for creating VietQR payment requests
    /// This is used internally when creating payment requests through VietQR
    /// </summary>
    public class VietQRPaymentRequestDto
    {
        [Required]
        public string OrderId { get; set; } = string.Empty;

        [Required]
        [Range(1000, long.MaxValue, ErrorMessage = "Amount must be at least 1000 VND")]
        public long Amount { get; set; }

        [Required]
        public string Description { get; set; } = string.Empty;

        public string? ReturnUrl { get; set; }

        public string? NotifyUrl { get; set; }

        public string? TerminalCode { get; set; }

        public string? SubTerminalCode { get; set; }

        public string? ServiceCode { get; set; }
    }

    /// <summary>
    /// Response DTO for VietQR payment request creation
    /// </summary>
    public class VietQRPaymentResponseDto
    {
        public bool IsSuccess { get; set; }
        public string? PaymentUrl { get; set; }
        public string? QRCode { get; set; }
        public string? TransactionReference { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ErrorCode { get; set; }
    }

    /// <summary>
    /// Configuration DTO for VietQR settings
    /// </summary>
    public class VietQRConfigurationDto
    {
        public string SecretKey { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string Issuer { get; set; } = string.Empty;
        public string Audience { get; set; } = string.Empty;
        public int TokenExpirationMinutes { get; set; } = 5;
        public string BankAccount { get; set; } = string.Empty;
        public string BankCode { get; set; } = string.Empty;
        public string MerchantName { get; set; } = string.Empty;
        public bool IsEnabled { get; set; } = false;
    }
}
