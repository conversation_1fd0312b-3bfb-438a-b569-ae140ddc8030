using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using RealEstate.Application.Interfaces;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace RealEstate.API.CustomAuthorizationPolicy
{
    /// <summary>
    /// Authorization handler for VietQR token validation.
    /// Validates that the token is a VietQR token and restricts access to VietQR endpoints only.
    /// </summary>
    public class VietQRTokenHandler : AuthorizationHandler<VietQRTokenRequirement>
    {
        private readonly ILogger<VietQRTokenHandler> _logger;
        private readonly IVietQRTokenService _vietQRTokenService;
        private readonly IConfiguration _configuration;

        public VietQRTokenHandler(
            ILogger<VietQRTokenHandler> logger,
            IVietQRTokenService vietQRTokenService,
            IConfiguration configuration)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _vietQRTokenService = vietQRTokenService ?? throw new ArgumentNullException(nameof(vietQRTokenService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context, 
            VietQRTokenRequirement requirement)
        {
            try
            {
                _logger.LogDebug("Validating VietQR token authorization...");

                // Check if user is authenticated
                if (context.User.Identity?.IsAuthenticated != true)
                {
                    _logger.LogWarning("VietQR authorization failed: User is not authenticated");
                    context.Fail(new AuthorizationFailureReason(this, "User is not authenticated"));
                    return;
                }

                // Get the authorization header from the HTTP context
                var httpContext = GetHttpContext(context);
                if (httpContext == null)
                {
                    _logger.LogWarning("VietQR authorization failed: HTTP context is not available");
                    context.Fail(new AuthorizationFailureReason(this, "HTTP context is not available"));
                    return;
                }

                // Extract the token from the Authorization header
                var authHeader = httpContext.Request.Headers.Authorization.FirstOrDefault();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    _logger.LogWarning("VietQR authorization failed: Missing or invalid Authorization header");
                    context.Fail(new AuthorizationFailureReason(this, "Missing or invalid Authorization header"));
                    return;
                }

                var token = authHeader.Substring("Bearer ".Length).Trim();

                // Validate the token using VietQR token service
                var tokenValidationResult = await _vietQRTokenService.ValidateTokenAsync(token);
                if (!tokenValidationResult.IsSuccess || !tokenValidationResult.Value)
                {
                    _logger.LogWarning("VietQR authorization failed: Token validation failed - {Error}", 
                        tokenValidationResult.ErrorMessage);
                    context.Fail(new AuthorizationFailureReason(this, "Invalid VietQR token"));
                    return;
                }

                // Parse the token to validate issuer and audience
                var tokenHandler = new JwtSecurityTokenHandler();
                var jsonToken = tokenHandler.ReadJwtToken(token);

                // Validate issuer
                var tokenIssuer = jsonToken.Issuer;
                if (string.IsNullOrEmpty(tokenIssuer) || !tokenIssuer.Equals(requirement.AllowedIssuer, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogWarning("VietQR authorization failed: Invalid issuer. Expected: {ExpectedIssuer}, Actual: {ActualIssuer}", 
                        requirement.AllowedIssuer, tokenIssuer);
                    context.Fail(new AuthorizationFailureReason(this, "Invalid token issuer"));
                    return;
                }

                // Validate audience
                var tokenAudiences = jsonToken.Audiences;
                if (!tokenAudiences.Any(aud => aud.Equals(requirement.AllowedAudience, StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogWarning("VietQR authorization failed: Invalid audience. Expected: {ExpectedAudience}, Actual: {ActualAudiences}", 
                        requirement.AllowedAudience, string.Join(", ", tokenAudiences));
                    context.Fail(new AuthorizationFailureReason(this, "Invalid token audience"));
                    return;
                }

                // Validate that this is specifically a VietQR token by checking custom claims
                var tokenTypeClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == "token_type")?.Value;
                if (string.IsNullOrEmpty(tokenTypeClaim) || !tokenTypeClaim.Equals("vietqr_payment_gateway", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogWarning("VietQR authorization failed: Token is not a VietQR payment gateway token. TokenType: {TokenType}", 
                        tokenTypeClaim);
                    context.Fail(new AuthorizationFailureReason(this, "Token is not a VietQR payment gateway token"));
                    return;
                }

                // Validate that the request is to a VietQR endpoint
                var requestPath = httpContext.Request.Path.Value;
                if (string.IsNullOrEmpty(requestPath) || !IsVietQREndpoint(requestPath))
                {
                    _logger.LogWarning("VietQR authorization failed: VietQR token cannot access non-VietQR endpoint. Path: {RequestPath}", 
                        requestPath);
                    context.Fail(new AuthorizationFailureReason(this, "VietQR token can only access VietQR endpoints"));
                    return;
                }

                // Log successful authorization
                var clientIp = httpContext.Connection.RemoteIpAddress?.ToString();
                _logger.LogInformation("VietQR token authorization successful for path: {RequestPath} from IP: {ClientIP}", 
                    requestPath, clientIp);

                // Authorization successful
                context.Succeed(requirement);
            }
            catch (SecurityTokenException ex)
            {
                _logger.LogWarning(ex, "VietQR authorization failed: Security token exception");
                context.Fail(new AuthorizationFailureReason(this, "Invalid security token"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "VietQR authorization failed: Unexpected error during token validation");
                context.Fail(new AuthorizationFailureReason(this, "Token validation error"));
            }
        }

        /// <summary>
        /// Extracts HTTP context from authorization context
        /// </summary>
        private HttpContext? GetHttpContext(AuthorizationHandlerContext context)
        {
            return context.Resource switch
            {
                HttpContext httpContext => httpContext,
                Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext filterContext => filterContext.HttpContext,
                _ => null
            };
        }

        /// <summary>
        /// Validates that the request path is a VietQR endpoint
        /// </summary>
        private bool IsVietQREndpoint(string requestPath)
        {
            // Define allowed VietQR endpoint patterns
            var allowedPaths = new[]
            {
                "/vietqr/transaction-sync",
                "/vietqr/health",
                "/vietqr/create-payment"
            };

            return allowedPaths.Any(path => 
                requestPath.Equals(path, StringComparison.OrdinalIgnoreCase));
        }
    }
}
