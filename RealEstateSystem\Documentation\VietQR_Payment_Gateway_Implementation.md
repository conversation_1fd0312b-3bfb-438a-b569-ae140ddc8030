# VietQR Payment Gateway Implementation

## Overview

This document describes the implementation of VietQR payment gateway integration for the YezHome Real Estate System. The implementation provides a complete solution for handling VietQR payments with comprehensive logging, security, and error handling.

## Architecture

### Components

1. **VietQRTokenService** - Handles authentication token generation and validation for VietQR
2. **VietQRPaymentGatewayService** - Manages payment requests and transaction processing
3. **VietQRController** - Provides API endpoints for VietQR integration
4. **VietQRLoggingService** - Specialized logging for payment tracking and audit trails
5. **DTOs** - Data transfer objects for VietQR API communication

### Key Features

- **Separate Token System**: Uses dedicated secret key separate from main JWT authentication
- **Comprehensive Logging**: Detailed logging for all payment operations, errors, and security events
- **Security**: Token validation, signature verification, and IP address tracking
- **Error Handling**: Robust error handling with proper HTTP status codes
- **Configuration**: Flexible configuration through appsettings.json

## API Endpoints

### 1. Token Generation Endpoint

**Endpoint**: `POST /api/vietqr/token_generate`

**Purpose**: VietQR calls this endpoint to obtain Bearer tokens for authentication.

**Authentication**: Basic Auth (username:password in Base64)

**Request Headers**:
```
Authorization: Basic <base64(username:password)>
Content-Type: application/json
```

**Response (Success)**:
```json
{
    "access_token": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 300
}
```

**Response (Error)**:
```json
{
    "status": "FAILED",
    "message": "Invalid credentials"
}
```

### 2. Transaction Sync Endpoint

**Endpoint**: `POST /api/vietqr/transaction-sync`

**Purpose**: VietQR calls this endpoint to notify about completed transactions.

**Authentication**: Bearer Token (obtained from token generation endpoint)

**Request Headers**:
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body**:
```json
{
    "bankaccount": "**********",
    "amount": 100000,
    "transType": "C",
    "content": "Payment for order ORDER123",
    "transactionid": "TXN123456789",
    "transactiontime": *************,
    "referencenumber": "REF123456",
    "orderId": "ORDER123",
    "terminalCode": "TERM001",
    "subTerminalCode": "SUBTERM001",
    "serviceCode": "SVC001",
    "urlLink": "https://example.com/success",
    "sign": "signature_hash"
}
```

**Response (Success)**:
```json
{
    "error": false,
    "errorReason": null,
    "toastMessage": "Transaction processed successfully",
    "object": {
        "reftransactionid": "REF_TXN123456789_20241012143000"
    }
}
```

**Response (Error)**:
```json
{
    "error": true,
    "errorReason": "TRANSACTION_PROCESSING_FAILED",
    "toastMessage": "Invalid transaction data",
    "object": null
}
```

### 3. Health Check Endpoint

**Endpoint**: `GET /api/vietqr/health`

**Purpose**: Health check for VietQR integration monitoring.

**Authentication**: None

**Response**:
```json
{
    "status": "healthy",
    "service": "VietQR Payment Gateway",
    "timestamp": "2024-10-12T14:30:00Z",
    "version": "1.0.0"
}
```

### 4. Create Payment Endpoint (Internal)

**Endpoint**: `POST /api/vietqr/create-payment`

**Purpose**: Internal endpoint for creating VietQR payment requests.

**Authentication**: JWT Bearer Token (user authentication)

**Request Body**:
```json
{
    "orderId": "ORDER123",
    "amount": 100000,
    "description": "Payment for property listing",
    "returnUrl": "https://localhost:3000/payment/success",
    "notifyUrl": "https://localhost:7057/api/vietqr/transaction-sync",
    "terminalCode": "TERM001",
    "serviceCode": "SVC001"
}
```

## Configuration

### appsettings.json

```json
{
    "VietQR": {
        "IsEnabled": true,
        "SecretKey": "vietqr_secret_key_256_bit_for_payment_gateway_authentication",
        "Username": "yezhome_vietqr_user",
        "Password": "yezhome_vietqr_password",
        "Issuer": "YezHome-VietQR-Gateway",
        "Audience": "VietQR-Client",
        "TokenExpirationMinutes": 5,
        "BankAccount": "**********",
        "BankCode": "970422",
        "MerchantName": "YEZHOME",
        "CallbackUrl": "https://localhost:7057/api/vietqr/transaction-sync",
        "ReturnUrl": "https://localhost:3000/payment/success",
        "CancelUrl": "https://localhost:3000/payment/cancel"
    }
}
```

### Configuration Parameters

- **IsEnabled**: Enable/disable VietQR payment gateway
- **SecretKey**: 256-bit secret key for token signing and signature validation
- **Username/Password**: Credentials for VietQR authentication
- **TokenExpirationMinutes**: Token expiration time (default: 5 minutes)
- **BankAccount**: Bank account number for receiving payments
- **BankCode**: Bank identifier code
- **MerchantName**: Merchant display name

## Logging

### Log Categories

1. **Payment Requests**: Creation, success, and failure of payment requests
2. **Transaction Sync**: Incoming transaction notifications from VietQR
3. **Authentication**: Token generation and validation events
4. **Security**: Suspicious activities, invalid signatures, unauthorized access
5. **Performance**: Operation duration and success rates
6. **System Errors**: Exceptions and system failures

### Log Format

All logs are structured in JSON format for easy parsing and analysis:

```json
{
    "EventType": "VietQR_Transaction_Processed",
    "TransactionId": "TXN123456789",
    "OrderId": "ORDER123",
    "Amount": 100000,
    "RefTransactionId": "REF_TXN123456789_20241012143000",
    "ProcessingTime": "2024-10-12T14:30:00Z",
    "ClientIP": "*************",
    "Timestamp": "2024-10-12T14:30:00Z"
}
```

### Log Levels

- **Information**: Normal operations, successful transactions
- **Warning**: Authentication failures, validation errors
- **Error**: System errors, transaction processing failures
- **Debug**: Detailed debugging information (token validation, QR generation)

## Security Features

### Authentication
- Basic Auth for token generation endpoint
- Bearer token authentication for transaction sync
- Separate secret key from main application JWT

### Validation
- Request data validation
- Token expiration checking
- Signature verification (optional)
- IP address logging for audit trails

### Rate Limiting
- Built-in ASP.NET Core rate limiting
- Configurable limits per endpoint
- IP-based rate limiting

## Error Handling

### Error Categories

1. **Authentication Errors** (401)
   - Invalid credentials
   - Expired tokens
   - Missing authorization headers

2. **Validation Errors** (400)
   - Invalid request format
   - Missing required fields
   - Invalid data types

3. **Processing Errors** (400)
   - Transaction processing failures
   - Wallet service errors
   - Business logic violations

4. **System Errors** (500)
   - Database connection issues
   - External service failures
   - Unexpected exceptions

### Error Response Format

```json
{
    "error": true,
    "errorReason": "ERROR_CODE",
    "toastMessage": "Human-readable error message",
    "object": null
}
```

## Integration with Existing System

### Wallet Service Integration
- Processes VietQR transactions through existing wallet service
- Maintains transaction history and audit trails
- Updates user balances and spending logs

### Notification System
- Sends payment confirmation notifications
- Integrates with existing email and push notification services
- Provides real-time payment status updates

### Property Management
- Links payments to property listings
- Handles property renewal payments
- Manages highlight fees and premium features

## Testing

### Unit Tests
- Token generation and validation
- Payment request creation
- Transaction processing logic
- Error handling scenarios

### Integration Tests
- End-to-end payment flow
- VietQR callback handling
- Database transaction integrity
- External service integration

### Security Tests
- Authentication bypass attempts
- Token manipulation tests
- Signature validation tests
- Rate limiting verification

## Deployment Considerations

### Environment Configuration
- Separate configurations for development, staging, and production
- Secure storage of secret keys and credentials
- Environment-specific bank account settings

### Monitoring
- Application performance monitoring
- Payment success/failure rates
- Error rate tracking
- Security event monitoring

### Backup and Recovery
- Transaction log backup
- Configuration backup
- Disaster recovery procedures

## Maintenance

### Regular Tasks
- Log rotation and archival
- Performance monitoring
- Security audit reviews
- Configuration updates

### Troubleshooting
- Common error scenarios and solutions
- Log analysis procedures
- Performance optimization guidelines
- Security incident response

## Support and Documentation

### API Documentation
- Swagger/OpenAPI documentation
- Postman collection for testing
- Integration examples and code samples

### Operational Documentation
- Deployment procedures
- Configuration management
- Monitoring and alerting setup
- Troubleshooting guides
