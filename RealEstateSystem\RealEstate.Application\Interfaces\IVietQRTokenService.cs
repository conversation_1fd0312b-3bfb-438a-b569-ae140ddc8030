using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    /// <summary>
    /// Service for generating and validating VietQR payment gateway tokens.
    /// This is separate from the main JWT authentication system.
    /// Handles both internal tokens and VietQR API tokens.
    /// </summary>
    public interface IVietQRTokenService
    {
        /// <summary>
        /// Generates a Bearer token for VietQR payment gateway authentication (internal use)
        /// </summary>
        /// <param name="username">The username provided by VietQR</param>
        /// <returns>Result containing the token response or error</returns>
        Task<Result<VietQRTokenResponse>> GenerateTokenAsync(string username);

        /// <summary>
        /// Validates a VietQR Bearer token (internal use)
        /// </summary>
        /// <param name="token">The Bearer token to validate</param>
        /// <returns>Result indicating if the token is valid</returns>
        Task<Result<bool>> ValidateTokenAsync(string token);

        /// <summary>
        /// Validates VietQR credentials (username and password)
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Result indicating if credentials are valid</returns>
        Task<Result<bool>> ValidateCredentialsAsync(string username, string password);

        /// <summary>
        /// Gets a valid token from VietQR API for making API calls
        /// Handles caching and automatic refresh
        /// </summary>
        /// <returns>Result containing a valid VietQR API token</returns>
        Task<Result<VietQRApiTokenResponse>> GetVietQRApiTokenAsync();

        /// <summary>
        /// Forces refresh of VietQR API token (bypasses cache)
        /// </summary>
        /// <returns>Result containing a fresh VietQR API token</returns>
        Task<Result<VietQRApiTokenResponse>> RefreshVietQRApiTokenAsync();
    }

    /// <summary>
    /// Response model for internal VietQR token generation
    /// </summary>
    public class VietQRTokenResponse
    {
        public string AccessToken { get; set; } = string.Empty;
        public string TokenType { get; set; } = "Bearer";
        public int ExpiresIn { get; set; }
    }

    /// <summary>
    /// Response model for VietQR API token
    /// </summary>
    public class VietQRApiTokenResponse
    {
        public string AccessToken { get; set; } = string.Empty;
        public string TokenType { get; set; } = "Bearer";
        public int ExpiresIn { get; set; }
        public DateTime ExpiresAt { get; set; }
        public bool IsExpired => DateTime.UtcNow >= ExpiresAt.AddMinutes(-1); // Refresh 1 minute before expiry
    }
}
