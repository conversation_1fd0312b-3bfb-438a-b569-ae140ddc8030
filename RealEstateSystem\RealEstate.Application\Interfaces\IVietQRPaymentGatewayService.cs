using RealEstate.Application.DTO.VietQR;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    /// <summary>
    /// Service interface for VietQR payment gateway operations
    /// </summary>
    public interface IVietQRPaymentGatewayService
    {
        /// <summary>
        /// Creates a VietQR payment request
        /// </summary>
        /// <param name="request">Payment request details</param>
        /// <returns>Result containing payment response or error</returns>
        Task<Result<VietQRPaymentResponseDto>> CreatePaymentRequestAsync(VietQRPaymentRequestDto request);

        /// <summary>
        /// Processes a transaction sync notification from VietQR
        /// </summary>
        /// <param name="transactionData">Transaction data from VietQR</param>
        /// <returns>Result containing transaction processing response</returns>
        Task<Result<VietQRTransactionSyncSuccessResponseDto>> ProcessTransactionSyncAsync(VietQRTransactionSyncRequestDto transactionData);

        /// <summary>
        /// Validates VietQR transaction signature (if applicable)
        /// </summary>
        /// <param name="transactionData">Transaction data to validate</param>
        /// <returns>Result indicating if signature is valid</returns>
        Task<Result<bool>> ValidateTransactionSignatureAsync(VietQRTransactionSyncRequestDto transactionData);

        /// <summary>
        /// Generates QR code for payment
        /// </summary>
        /// <param name="amount">Payment amount</param>
        /// <param name="content">Payment description</param>
        /// <param name="orderId">Order identifier</param>
        /// <returns>Result containing QR code data</returns>
        Task<Result<string>> GenerateQRCodeAsync(long amount, string content, string orderId);

        /// <summary>
        /// Generates VietQR code via VietQR API
        /// </summary>
        /// <param name="request">QR generation request</param>
        /// <returns>Result containing VietQR API response</returns>
        Task<Result<VietQRGenerateCodeResponseDto>> GenerateVietQRCodeAsync(VietQRGenerateCodeRequestDto request);
    }
}
