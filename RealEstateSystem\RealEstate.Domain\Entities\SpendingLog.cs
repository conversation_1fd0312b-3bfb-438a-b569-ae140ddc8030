using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class SpendingLog : BaseEntity
    {
        [Required]
        public Guid PropertyId { get; set; }

        [Required]
        public Guid UserId { get; set; }

        [Required]
        [Column(TypeName = "numeric(20,2)")]
        public decimal Amount { get; set; }

        [Required]
        [MaxLength(50)]
        public string SpendingType { get; set; } = string.Empty; // 'renew', 'highlight', etc.

        public Guid? TransactionId { get; set; }

        [Required]
        public DateTimeOffset SpentAt { get; set; } = DateTime.UtcNow;

        [Column(TypeName = "jsonb")]
        public string? Details { get; set; } // For storing additional details like duration, highlight type, etc.

        [Required]
        public DateTimeOffset CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTimeOffset? UpdatedAt { get; set; }

        // Navigation properties
        [ForeignKey("PropertyId")]
        public Property? Property { get; set; }

        [ForeignKey("UserId")]
        public AppUser? User { get; set; }

        [ForeignKey("TransactionId")]
        public WalletTransaction? Transaction { get; set; }
    }
}
