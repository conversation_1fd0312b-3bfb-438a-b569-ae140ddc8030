using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace RealEstate.Application.DTO.VietQR
{
    /// <summary>
    /// Request DTO for VietQR API token generation
    /// </summary>
    public class VietQRApiTokenRequestDto
    {
        // VietQR API uses Basic Authentication, so no body parameters needed
        // Username and password are sent in Authorization header
    }

    /// <summary>
    /// Response DTO from VietQR API token generation
    /// </summary>
    public class VietQRApiTokenResponseDto
    {
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;

        [JsonPropertyName("token_type")]
        public string TokenType { get; set; } = "Bearer";

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }
    }

    /// <summary>
    /// Error response from VietQR API token generation
    /// </summary>
    public class VietQRApiTokenErrorResponseDto
    {
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request DTO for VietQR Generate QR Code API
    /// </summary>
    public class VietQRGenerateCodeRequestDto
    {
        /// <summary>
        /// Bank code (required)
        /// </summary>
        [Required]
        [JsonPropertyName("bankCode")]
        public string BankCode { get; set; } = string.Empty;

        /// <summary>
        /// Bank account number (required)
        /// </summary>
        [Required]
        [JsonPropertyName("bankAccount")]
        public string BankAccount { get; set; } = string.Empty;

        /// <summary>
        /// Account holder name - no Vietnamese diacritics (required)
        /// </summary>
        [Required]
        [JsonPropertyName("userBankName")]
        public string UserBankName { get; set; } = string.Empty;

        /// <summary>
        /// Payment content - max 23 characters, no special characters (required)
        /// </summary>
        [Required]
        [StringLength(23)]
        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// QR type: 0=Dynamic, 1=Static, 3=Semi-Dynamic (required)
        /// </summary>
        [Required]
        [JsonPropertyName("qrType")]
        public int QrType { get; set; }

        /// <summary>
        /// Payment amount - required if qrType = 0 or 3
        /// </summary>
        [JsonPropertyName("amount")]
        public long? Amount { get; set; }

        /// <summary>
        /// Order ID - max 13 characters, required if qrType = 0
        /// </summary>
        [StringLength(13)]
        [JsonPropertyName("orderId")]
        public string? OrderId { get; set; }

        /// <summary>
        /// Transaction type: D=Debit, C=Credit - required if qrType = 0
        /// </summary>
        [JsonPropertyName("transType")]
        public string? TransType { get; set; } = "C";

        /// <summary>
        /// Terminal code - required if qrType = 1 or 3
        /// </summary>
        [JsonPropertyName("terminalCode")]
        public string? TerminalCode { get; set; }

        /// <summary>
        /// Service code - required if qrType = 3
        /// </summary>
        [JsonPropertyName("serviceCode")]
        public string? ServiceCode { get; set; }

        /// <summary>
        /// Sub terminal code (optional)
        /// </summary>
        [JsonPropertyName("subTerminalCode")]
        public string? SubTerminalCode { get; set; }

        /// <summary>
        /// Signature (optional)
        /// </summary>
        [JsonPropertyName("sign")]
        public string? Sign { get; set; }

        /// <summary>
        /// Redirect URL after payment (optional)
        /// </summary>
        [JsonPropertyName("urlLink")]
        public string? UrlLink { get; set; }

        /// <summary>
        /// Transaction note (optional)
        /// </summary>
        [JsonPropertyName("note")]
        public string? Note { get; set; }

        /// <summary>
        /// Additional data parameters (optional)
        /// </summary>
        [JsonPropertyName("additionalDataList")]
        public List<object>? AdditionalDataList { get; set; }
    }

    /// <summary>
    /// Response DTO from VietQR Generate QR Code API
    /// </summary>
    public class VietQRGenerateCodeResponseDto
    {
        [JsonPropertyName("bankCode")]
        public string BankCode { get; set; } = string.Empty;

        [JsonPropertyName("bankName")]
        public string BankName { get; set; } = string.Empty;

        [JsonPropertyName("bankAccount")]
        public string BankAccount { get; set; } = string.Empty;

        [JsonPropertyName("userBankName")]
        public string UserBankName { get; set; } = string.Empty;

        [JsonPropertyName("amount")]
        public string Amount { get; set; } = string.Empty;

        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        [JsonPropertyName("qrCode")]
        public string QrCode { get; set; } = string.Empty;

        [JsonPropertyName("imgId")]
        public string ImgId { get; set; } = string.Empty;

        [JsonPropertyName("existing")]
        public int Existing { get; set; }

        [JsonPropertyName("transactionId")]
        public string TransactionId { get; set; } = string.Empty;

        [JsonPropertyName("transactionRefId")]
        public string TransactionRefId { get; set; } = string.Empty;

        [JsonPropertyName("qrLink")]
        public string QrLink { get; set; } = string.Empty;

        [JsonPropertyName("terminalCode")]
        public string? TerminalCode { get; set; }

        [JsonPropertyName("subTerminalCode")]
        public string? SubTerminalCode { get; set; }

        [JsonPropertyName("serviceCode")]
        public string? ServiceCode { get; set; }

        [JsonPropertyName("orderId")]
        public string? OrderId { get; set; }

        [JsonPropertyName("additionalData")]
        public List<object>? AdditionalData { get; set; }

        [JsonPropertyName("vaAccount")]
        public string? VaAccount { get; set; }
    }

    /// <summary>
    /// Error response from VietQR Generate QR Code API
    /// </summary>
    public class VietQRGenerateCodeErrorResponseDto
    {
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// Enum for VietQR QR types
    /// </summary>
    public enum VietQRType
    {
        /// <summary>
        /// Dynamic QR - with specific amount, expires after payment
        /// </summary>
        Dynamic = 0,

        /// <summary>
        /// Static QR - without amount, reusable
        /// </summary>
        Static = 1,

        /// <summary>
        /// Semi-Dynamic QR - with amount but reusable
        /// </summary>
        SemiDynamic = 3
    }
}
