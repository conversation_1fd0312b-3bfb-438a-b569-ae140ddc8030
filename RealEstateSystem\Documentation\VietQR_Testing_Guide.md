# VietQR Payment Gateway Testing Guide

## Overview

This guide provides comprehensive testing procedures for the VietQR payment gateway integration in the YezHome Real Estate System.

## Prerequisites

1. **Development Environment Setup**
   - .NET 8.0 SDK installed
   - PostgreSQL database running
   - Application configured with VietQR settings in appsettings.Development.json

2. **Configuration Verification**
   ```json
   {
     "VietQR": {
       "IsEnabled": true,
       "SecretKey": "vietqr_dev_secret_key_256_bit...",
       "Username": "yezhome_vietqr_dev_user",
       "Password": "yezhome_vietqr_dev_password_123456",
       "TokenExpirationMinutes": 5,
       "BankAccount": "**********",
       "BankCode": "970422"
     }
   }
   ```

## Testing Scenarios

### 1. Token Generation Testing

#### Test Case 1.1: Valid Credentials
**Endpoint**: `POST /api/vietqr/token_generate`

**Request**:
```bash
curl -X POST "https://localhost:7057/api/vietqr/token_generate" \
  -H "Authorization: Basic $(echo -n 'yezhome_vietqr_dev_user:yezhome_vietqr_dev_password_123456' | base64)" \
  -H "Content-Type: application/json"
```

**Expected Response** (200 OK):
```json
{
    "access_token": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 300
}
```

#### Test Case 1.2: Invalid Credentials
**Request**:
```bash
curl -X POST "https://localhost:7057/api/vietqr/token_generate" \
  -H "Authorization: Basic $(echo -n 'invalid_user:invalid_password' | base64)" \
  -H "Content-Type: application/json"
```

**Expected Response** (401 Unauthorized):
```json
{
    "status": "FAILED",
    "message": "Invalid credentials"
}
```

#### Test Case 1.3: Missing Authorization Header
**Request**:
```bash
curl -X POST "https://localhost:7057/api/vietqr/token_generate" \
  -H "Content-Type: application/json"
```

**Expected Response** (400 Bad Request):
```json
{
    "status": "FAILED",
    "message": "Authorization header is missing or invalid"
}
```

### 2. Transaction Sync Testing

#### Test Case 2.1: Valid Transaction
**Step 1**: Get a valid token from Test Case 1.1

**Step 2**: Send transaction sync request
```bash
curl -X POST "https://localhost:7057/api/vietqr/transaction-sync" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "bankaccount": "**********",
    "amount": 100000,
    "transType": "C",
    "content": "Payment for order TEST123",
    "transactionid": "TXN123456789",
    "transactiontime": *************,
    "referencenumber": "REF123456",
    "orderId": "ORDER123",
    "terminalCode": "TERM001",
    "serviceCode": "SVC001"
  }'
```

**Expected Response** (200 OK):
```json
{
    "error": false,
    "errorReason": null,
    "toastMessage": "Transaction processed successfully",
    "object": {
        "reftransactionid": "REF_TXN123456789_20241012143000"
    }
}
```

#### Test Case 2.2: Invalid Token
**Request**:
```bash
curl -X POST "https://localhost:7057/api/vietqr/transaction-sync" \
  -H "Authorization: Bearer invalid_token" \
  -H "Content-Type: application/json" \
  -d '{
    "bankaccount": "**********",
    "amount": 100000,
    "transType": "C",
    "content": "Payment for order TEST123",
    "transactionid": "TXN123456789",
    "transactiontime": *************,
    "referencenumber": "REF123456",
    "orderId": "ORDER123"
  }'
```

**Expected Response** (401 Unauthorized):
```json
{
    "error": true,
    "errorReason": "INVALID_TOKEN",
    "toastMessage": "Invalid or expired token",
    "object": null
}
```

#### Test Case 2.3: Missing Required Fields
**Request**:
```bash
curl -X POST "https://localhost:7057/api/vietqr/transaction-sync" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100000,
    "transType": "C"
  }'
```

**Expected Response** (400 Bad Request):
```json
{
    "error": true,
    "errorReason": "TRANSACTION_PROCESSING_FAILED",
    "toastMessage": "TransactionId is required",
    "object": null
}
```

### 3. Health Check Testing

#### Test Case 3.1: Health Check
**Request**:
```bash
curl -X GET "https://localhost:7057/api/vietqr/health"
```

**Expected Response** (200 OK):
```json
{
    "status": "healthy",
    "service": "VietQR Payment Gateway",
    "timestamp": "2024-10-12T14:30:00Z",
    "version": "1.0.0"
}
```

### 4. Payment Creation Testing (Internal)

#### Test Case 4.1: Create Payment Request
**Prerequisites**: Valid user JWT token

**Request**:
```bash
curl -X POST "https://localhost:7057/api/vietqr/create-payment" \
  -H "Authorization: Bearer YOUR_USER_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "ORDER123",
    "amount": 100000,
    "description": "Payment for property listing",
    "returnUrl": "https://localhost:3000/payment/success",
    "terminalCode": "TERM001",
    "serviceCode": "SVC001"
  }'
```

**Expected Response** (200 OK):
```json
{
    "isSuccess": true,
    "qrCode": "base64_encoded_qr_data",
    "transactionReference": "VIETQR_ORDER123_20241012143000_abc12345",
    "paymentUrl": null
}
```

## Automated Testing Scripts

### PowerShell Script for Windows

```powershell
# VietQR Testing Script
$baseUrl = "https://localhost:7057"
$credentials = "yezhome_vietqr_dev_user:yezhome_vietqr_dev_password_123456"
$encodedCredentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes($credentials))

Write-Host "Testing VietQR Payment Gateway..." -ForegroundColor Green

# Test 1: Token Generation
Write-Host "`n1. Testing Token Generation..." -ForegroundColor Yellow
$tokenResponse = Invoke-RestMethod -Uri "$baseUrl/api/vietqr/token_generate" `
    -Method POST `
    -Headers @{
        "Authorization" = "Basic $encodedCredentials"
        "Content-Type" = "application/json"
    }

if ($tokenResponse.access_token) {
    Write-Host "✓ Token generation successful" -ForegroundColor Green
    $token = $tokenResponse.access_token
} else {
    Write-Host "✗ Token generation failed" -ForegroundColor Red
    exit 1
}

# Test 2: Health Check
Write-Host "`n2. Testing Health Check..." -ForegroundColor Yellow
$healthResponse = Invoke-RestMethod -Uri "$baseUrl/api/vietqr/health" -Method GET

if ($healthResponse.status -eq "healthy") {
    Write-Host "✓ Health check passed" -ForegroundColor Green
} else {
    Write-Host "✗ Health check failed" -ForegroundColor Red
}

# Test 3: Transaction Sync
Write-Host "`n3. Testing Transaction Sync..." -ForegroundColor Yellow
$transactionData = @{
    bankaccount = "**********"
    amount = 100000
    transType = "C"
    content = "Test payment"
    transactionid = "TEST$(Get-Date -Format 'yyyyMMddHHmmss')"
    transactiontime = [DateTimeOffset]::UtcNow.ToUnixTimeMilliseconds()
    referencenumber = "REF$(Get-Random)"
    orderId = "ORDER$(Get-Random)"
} | ConvertTo-Json

$syncResponse = Invoke-RestMethod -Uri "$baseUrl/api/vietqr/transaction-sync" `
    -Method POST `
    -Headers @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    } `
    -Body $transactionData

if (-not $syncResponse.error) {
    Write-Host "✓ Transaction sync successful" -ForegroundColor Green
} else {
    Write-Host "✗ Transaction sync failed: $($syncResponse.toastMessage)" -ForegroundColor Red
}

Write-Host "`nTesting completed!" -ForegroundColor Green
```

### Bash Script for Linux/macOS

```bash
#!/bin/bash

# VietQR Testing Script
BASE_URL="https://localhost:7057"
CREDENTIALS="yezhome_vietqr_dev_user:yezhome_vietqr_dev_password_123456"
ENCODED_CREDENTIALS=$(echo -n "$CREDENTIALS" | base64)

echo "Testing VietQR Payment Gateway..."

# Test 1: Token Generation
echo -e "\n1. Testing Token Generation..."
TOKEN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/vietqr/token_generate" \
    -H "Authorization: Basic $ENCODED_CREDENTIALS" \
    -H "Content-Type: application/json")

TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')

if [ "$TOKEN" != "null" ] && [ "$TOKEN" != "" ]; then
    echo "✓ Token generation successful"
else
    echo "✗ Token generation failed"
    echo "$TOKEN_RESPONSE"
    exit 1
fi

# Test 2: Health Check
echo -e "\n2. Testing Health Check..."
HEALTH_RESPONSE=$(curl -s -X GET "$BASE_URL/api/vietqr/health")
HEALTH_STATUS=$(echo "$HEALTH_RESPONSE" | jq -r '.status')

if [ "$HEALTH_STATUS" = "healthy" ]; then
    echo "✓ Health check passed"
else
    echo "✗ Health check failed"
    echo "$HEALTH_RESPONSE"
fi

# Test 3: Transaction Sync
echo -e "\n3. Testing Transaction Sync..."
TRANSACTION_ID="TEST$(date +%Y%m%d%H%M%S)"
ORDER_ID="ORDER$RANDOM"
TRANSACTION_TIME=$(date +%s)000

TRANSACTION_DATA=$(cat <<EOF
{
    "bankaccount": "**********",
    "amount": 100000,
    "transType": "C",
    "content": "Test payment",
    "transactionid": "$TRANSACTION_ID",
    "transactiontime": $TRANSACTION_TIME,
    "referencenumber": "REF$RANDOM",
    "orderId": "$ORDER_ID"
}
EOF
)

SYNC_RESPONSE=$(curl -s -X POST "$BASE_URL/api/vietqr/transaction-sync" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "$TRANSACTION_DATA")

ERROR_STATUS=$(echo "$SYNC_RESPONSE" | jq -r '.error')

if [ "$ERROR_STATUS" = "false" ]; then
    echo "✓ Transaction sync successful"
else
    echo "✗ Transaction sync failed"
    echo "$SYNC_RESPONSE"
fi

echo -e "\nTesting completed!"
```

## Log Verification

### Check Application Logs

1. **View logs in development**:
   ```bash
   # If using console logging
   dotnet run --project RealEstate.API
   
   # Look for VietQR-related log entries
   ```

2. **Search for specific log events**:
   ```bash
   # Search for VietQR events in log files
   grep "VietQR" logs/application.log
   
   # Search for specific event types
   grep "VietQR_Token_Generated" logs/application.log
   grep "VietQR_Transaction_Processed" logs/application.log
   ```

### Expected Log Entries

1. **Token Generation**:
   ```json
   {
     "EventType": "VietQR_Token_Generated",
     "Username": "yezhome_vietqr_dev_user",
     "ExpiresIn": 300,
     "ClientIP": "127.0.0.1",
     "Timestamp": "2024-10-12T14:30:00Z"
   }
   ```

2. **Transaction Processing**:
   ```json
   {
     "EventType": "VietQR_Transaction_Processed",
     "TransactionId": "TXN123456789",
     "OrderId": "ORDER123",
     "Amount": 100000,
     "RefTransactionId": "REF_TXN123456789_20241012143000",
     "Timestamp": "2024-10-12T14:30:00Z"
   }
   ```

## Performance Testing

### Load Testing with Artillery

1. **Install Artillery**:
   ```bash
   npm install -g artillery
   ```

2. **Create test configuration** (artillery-vietqr.yml):
   ```yaml
   config:
     target: 'https://localhost:7057'
     phases:
       - duration: 60
         arrivalRate: 10
   scenarios:
     - name: "VietQR Token Generation"
       requests:
         - post:
             url: "/api/vietqr/token_generate"
             headers:
               Authorization: "Basic eWV6aG9tZV92aWV0cXJfZGV2X3VzZXI6eWV6aG9tZV92aWV0cXJfZGV2X3Bhc3N3b3JkXzEyMzQ1Ng=="
   ```

3. **Run load test**:
   ```bash
   artillery run artillery-vietqr.yml
   ```

## Troubleshooting

### Common Issues

1. **Token Generation Fails**
   - Check credentials in appsettings.json
   - Verify Base64 encoding of credentials
   - Check application logs for authentication errors

2. **Transaction Sync Fails**
   - Verify token is valid and not expired
   - Check request body format
   - Verify all required fields are present

3. **Health Check Fails**
   - Check if application is running
   - Verify endpoint routing
   - Check for any startup errors

### Debug Mode

Enable debug logging in appsettings.Development.json:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "RealEstate.Infrastructure.Services.PaymentGateway": "Debug"
    }
  }
}
```

## Security Testing

### Authentication Testing
- Test with invalid credentials
- Test with malformed Authorization headers
- Test token expiration scenarios

### Input Validation Testing
- Test with malformed JSON
- Test with missing required fields
- Test with invalid data types
- Test with extremely large values

### Rate Limiting Testing
- Send multiple rapid requests
- Verify rate limiting responses
- Test different IP addresses
