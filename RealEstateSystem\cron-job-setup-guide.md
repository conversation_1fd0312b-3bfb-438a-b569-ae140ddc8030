# Hướng dẫn cài đặt Cron Job cho YezHome Property Expiration Check

## Tổng quan

Tài liệu này hướng dẫn cách thiết lập cron job trên server Linux để tự động kiểm tra và gửi thông báo cho các bài đăng bất động sản sắp hết hạn hoặc đã hết hạn.

## Yêu cầu hệ thống

- Server Linux (Ubuntu/CentOS/Debian)
- YezHome Internal API đang chạy trên localhost:8080
- Quyền sudo hoặc root access
- curl đã được cài đặt

## Bước 1: Chuẩn bị file script

### 1.1. Upload file script lên server

Sao chép file `run_notification_check.sh` lên server vào thư mục `/opt/yezhome/scripts/`:

```bash
# Tạo thư mục scripts
sudo mkdir -p /opt/yezhome/scripts

# Copy file script (thay thế bằng phương pháp upload phù hợp)
sudo cp run_notification_check.sh /opt/yezhome/scripts/

# Hoặc tạo file trực tiếp trên server
sudo nano /opt/yezhome/scripts/run_notification_check.sh
# Sau đó paste nội dung file script vào
```

### 1.2. Cấp quyền thực thi cho script

```bash
sudo chmod +x /opt/yezhome/scripts/run_notification_check.sh
```

### 1.3. Tạo thư mục log

```bash
sudo mkdir -p /var/log/yezhome
sudo chown $USER:$USER /var/log/yezhome
```

## Bước 2: Kiểm tra script hoạt động

### 2.1. Test script thủ công

```bash
# Chạy script để kiểm tra
/opt/yezhome/scripts/run_notification_check.sh

# Kiểm tra log
tail -f /var/log/yezhome/notification_check.log
```

### 2.2. Kiểm tra kết quả mong đợi

Script sẽ:
- Ghi log với timestamp
- Gọi API endpoint
- Trả về mã lỗi 0 nếu thành công, khác 0 nếu thất bại

## Bước 3: Cài đặt Cron Job

### 3.1. Mở crontab editor

```bash
crontab -e
```

Nếu lần đầu chạy, hệ thống sẽ hỏi chọn editor. Khuyến nghị chọn nano (option 1).

### 3.2. Thêm cron job entry

Thêm dòng sau vào cuối file crontab:

```bash
# YezHome Property Expiration Check - Runs daily at 1:00 AM
0 1 * * * /opt/yezhome/scripts/run_notification_check.sh >/dev/null 2>&1
```

**Giải thích cú pháp cron:**
- `0`: Phút (0 = phút thứ 0)
- `1`: Giờ (1 = 1:00 AM)
- `*`: Ngày trong tháng (mọi ngày)
- `*`: Tháng (mọi tháng)
- `*`: Ngày trong tuần (mọi ngày trong tuần)
- `/opt/yezhome/scripts/run_notification_check.sh`: Đường dẫn đến script
- `>/dev/null 2>&1`: Chuyển hướng output để tránh spam email

### 3.3. Lưu và thoát

- Trong nano: `Ctrl + X`, sau đó `Y`, sau đó `Enter`
- Trong vim: `:wq`

### 3.4. Xác nhận cron job đã được thêm

```bash
crontab -l
```

## Bước 4: Các lệnh quản lý Cron Job

### 4.1. Xem danh sách cron jobs

```bash
crontab -l
```

### 4.2. Chỉnh sửa cron jobs

```bash
crontab -e
```

### 4.3. Xóa tất cả cron jobs

```bash
crontab -r
```

### 4.4. Kiểm tra cron service

```bash
# Kiểm tra trạng thái cron service
sudo systemctl status cron

# Khởi động cron service (nếu chưa chạy)
sudo systemctl start cron

# Kích hoạt cron service tự động khởi động
sudo systemctl enable cron
```

## Bước 5: Monitoring và Troubleshooting

### 5.1. Kiểm tra log của cron job

```bash
# Xem log của script
tail -f /var/log/yezhome/notification_check.log

# Xem log hệ thống cron
sudo tail -f /var/log/syslog | grep CRON
```

### 5.2. Test cron job thủ công

```bash
# Chạy script như cron sẽ chạy
/opt/yezhome/scripts/run_notification_check.sh

# Kiểm tra exit code
echo $?
# 0 = thành công, khác 0 = thất bại
```

### 5.3. Các vấn đề thường gặp

#### Script không chạy:
- Kiểm tra quyền thực thi: `ls -la /opt/yezhome/scripts/run_notification_check.sh`
- Kiểm tra đường dẫn trong crontab
- Kiểm tra cron service đang chạy

#### API không phản hồi:
- Kiểm tra YezHome Internal API đang chạy: `curl http://localhost:8080/healthz`
- Kiểm tra API key trong script khớp với appsettings.json
- Kiểm tra firewall không block localhost

#### Log không được tạo:
- Kiểm tra quyền ghi vào thư mục `/var/log/yezhome/`
- Tạo thư mục nếu chưa có: `sudo mkdir -p /var/log/yezhome`

## Bước 6: Tùy chỉnh thời gian chạy

### 6.1. Các ví dụ về cron schedule

```bash
# Chạy mỗi ngày lúc 2:30 AM
30 2 * * * /opt/yezhome/scripts/run_notification_check.sh

# Chạy mỗi 6 giờ
0 */6 * * * /opt/yezhome/scripts/run_notification_check.sh

# Chỉ chạy vào thứ 2 đến thứ 6 lúc 1:00 AM
0 1 * * 1-5 /opt/yezhome/scripts/run_notification_check.sh

# Chạy vào ngày đầu tiên của mỗi tháng lúc 1:00 AM
0 1 1 * * /opt/yezhome/scripts/run_notification_check.sh
```

### 6.2. Công cụ tạo cron expression

Sử dụng các website sau để tạo cron expression:
- https://crontab.guru/
- https://cron-job.org/en/

## Bước 7: Backup và Recovery

### 7.1. Backup crontab

```bash
# Backup crontab hiện tại
crontab -l > ~/crontab_backup_$(date +%Y%m%d).txt
```

### 7.2. Restore crontab

```bash
# Restore từ backup
crontab ~/crontab_backup_20241201.txt
```

## Bước 8: Security Best Practices

1. **Giới hạn quyền truy cập script:**
   ```bash
   sudo chown root:root /opt/yezhome/scripts/run_notification_check.sh
   sudo chmod 755 /opt/yezhome/scripts/run_notification_check.sh
   ```

2. **Bảo vệ API key:**
   - Không commit API key vào version control
   - Sử dụng environment variables nếu cần
   - Thay đổi API key định kỳ

3. **Monitor log files:**
   - Thiết lập log rotation để tránh log files quá lớn
   - Thiết lập alerts khi script thất bại

## Kết luận

Sau khi hoàn thành các bước trên, hệ thống sẽ tự động kiểm tra và gửi thông báo cho các bài đăng sắp hết hạn mỗi ngày lúc 1:00 AM. Hãy thường xuyên kiểm tra log để đảm bảo hệ thống hoạt động ổn định.
