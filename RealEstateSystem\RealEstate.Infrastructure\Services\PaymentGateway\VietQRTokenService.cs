using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using RealEstate.Application.DTO.VietQR;
using RealEstate.Application.Interfaces;
using Shared.Results;

namespace RealEstate.Infrastructure.Services.PaymentGateway
{
    /// <summary>
    /// Token service specifically for VietQR payment gateway authentication.
    /// Uses a separate secret key from the main JWT authentication system.
    /// Handles both internal tokens and VietQR API tokens.
    /// </summary>
    public class VietQRTokenService : IVietQRTokenService
    {
        private readonly IConfiguration _config;
        private readonly ILogger<VietQRTokenService> _logger;
        private readonly VietQRLoggingService _vietQRLogger;
        private readonly HttpClient _httpClient;
        private readonly SymmetricSecurityKey _key;
        private readonly string _validUsername;
        private readonly string _validPassword;
        private readonly int _tokenExpirationMinutes;
        private readonly string _vietQRApiBaseUrl;
        private readonly int _apiTimeoutSeconds;

        // Token caching
        private VietQRApiTokenResponse? _cachedApiToken;
        private readonly SemaphoreSlim _tokenSemaphore = new(1, 1);

        public VietQRTokenService(IConfiguration config, ILogger<VietQRTokenService> logger, VietQRLoggingService vietQRLogger, HttpClient httpClient)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _vietQRLogger = vietQRLogger ?? throw new ArgumentNullException(nameof(vietQRLogger));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));

            // Get VietQR-specific configuration
            var vietQRKey = _config["VietQR:SecretKey"] ??
                throw new InvalidOperationException("VietQR:SecretKey is not configured in appsettings.json");

            _validUsername = _config["VietQR:Username"] ??
                throw new InvalidOperationException("VietQR:Username is not configured in appsettings.json");

            _validPassword = _config["VietQR:Password"] ??
                throw new InvalidOperationException("VietQR:Password is not configured in appsettings.json");

            _tokenExpirationMinutes = _config.GetValue<int>("VietQR:TokenExpirationMinutes", 5);

            // VietQR API configuration
            var isProduction = _config.GetValue<bool>("VietQR:IsProduction", false);
            _vietQRApiBaseUrl = isProduction
                ? _config["VietQR:Api:BaseUrlProd"] ?? "https://api.vietqr.org"
                : _config["VietQR:Api:BaseUrlDev"] ?? "https://dev.vietqr.org";

            _apiTimeoutSeconds = _config.GetValue<int>("VietQR:Api:TimeoutSeconds", 30);

            _key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(vietQRKey));

            // Configure HTTP client
            _httpClient.BaseAddress = new Uri(_vietQRApiBaseUrl);
            _httpClient.Timeout = TimeSpan.FromSeconds(_apiTimeoutSeconds);

            _logger.LogInformation("VietQRTokenService initialized. Internal token expiration: {ExpirationMinutes} minutes, API Base URL: {BaseUrl}",
                _tokenExpirationMinutes, _vietQRApiBaseUrl);
        }

        public async Task<Result<VietQRTokenResponse>> GenerateTokenAsync(string username)
        {
            try
            {
                _logger.LogInformation("Generating VietQR token for username: {Username}", username);

                if (string.IsNullOrEmpty(username))
                {
                    _logger.LogWarning("Token generation failed: Username is null or empty");
                    return Result<VietQRTokenResponse>.Failure("Username is required", Shared.Enums.ErrorType.Validation);
                }

                var claims = new List<Claim>
                {
                    new Claim(JwtRegisteredClaimNames.Sub, username),
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                    new Claim(JwtRegisteredClaimNames.Iat,
                        new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString(),
                        ClaimValueTypes.Integer64),
                    new Claim("token_type", "vietqr_payment_gateway"),
                    new Claim("scope", "vietqr_endpoints_only") // Scope limitation for VietQR endpoints only
                };

                var creds = new SigningCredentials(_key, SecurityAlgorithms.HmacSha512Signature);
                var expirationTime = DateTime.UtcNow.AddMinutes(_tokenExpirationMinutes);

                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(claims),
                    Expires = expirationTime,
                    SigningCredentials = creds,
                    Issuer = _config["VietQR:Issuer"] ?? "VietQR-PaymentGateway",
                    Audience = _config["VietQR:Audience"] ?? "VietQR-Client"
                };

                var tokenHandler = new JwtSecurityTokenHandler();
                var token = tokenHandler.CreateToken(tokenDescriptor);
                var tokenString = tokenHandler.WriteToken(token);

                var response = new VietQRTokenResponse
                {
                    AccessToken = tokenString,
                    TokenType = "Bearer",
                    ExpiresIn = _tokenExpirationMinutes * 60 // Convert to seconds
                };

                _logger.LogInformation("VietQR token generated successfully for username: {Username}, expires at: {ExpirationTime}",
                    username, expirationTime);

                // Log with specialized VietQR logger
                _vietQRLogger.LogTokenGenerated(username, response.ExpiresIn);

                return Result<VietQRTokenResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating VietQR token for username: {Username}", username);
                return Result<VietQRTokenResponse>.Failure("Failed to generate token", Shared.Enums.ErrorType.Internal);
            }
        }

        public async Task<Result<bool>> ValidateTokenAsync(string token)
        {
            try
            {
                _logger.LogDebug("Validating VietQR token");

                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("Token validation failed: Token is null or empty");
                    return Result<bool>.Failure("Token is required", Shared.Enums.ErrorType.Validation);
                }

                var tokenHandler = new JwtSecurityTokenHandler();

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = _key,
                    ValidateIssuer = true,
                    ValidIssuer = _config["VietQR:Issuer"] ?? "VietQR-PaymentGateway",
                    ValidateAudience = true,
                    ValidAudience = _config["VietQR:Audience"] ?? "VietQR-Client",
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

                // Additional validation: check if it's a VietQR payment gateway token
                var tokenTypeClaim = principal.FindFirst("token_type");
                if (tokenTypeClaim?.Value != "vietqr_payment_gateway")
                {
                    _logger.LogWarning("Token validation failed: Invalid token type");
                    return Result<bool>.Failure("Invalid token type", Shared.Enums.ErrorType.Unauthorized);
                }

                _logger.LogDebug("VietQR token validated successfully");
                return Result<bool>.Success(true);
            }
            catch (SecurityTokenExpiredException ex)
            {
                _logger.LogWarning("VietQR token validation failed: Token expired - {Message}", ex.Message);
                return Result<bool>.Failure("Token has expired", Shared.Enums.ErrorType.Unauthorized);
            }
            catch (SecurityTokenException ex)
            {
                _logger.LogWarning("VietQR token validation failed: Invalid token - {Message}", ex.Message);
                return Result<bool>.Failure("Invalid token", Shared.Enums.ErrorType.Unauthorized);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating VietQR token");
                return Result<bool>.Failure("Token validation failed", Shared.Enums.ErrorType.Unauthorized);
            }
        }

        public async Task<Result<bool>> ValidateCredentialsAsync(string username, string password)
        {
            try
            {
                _logger.LogInformation("Validating VietQR credentials for username: {Username}", username);

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    _logger.LogWarning("Credential validation failed: Username or password is null or empty");
                    return Result<bool>.Failure("Username and password are required", Shared.Enums.ErrorType.Validation);
                }

                // Validate against configured credentials
                bool isValid = username == _validUsername && password == _validPassword;

                if (isValid)
                {
                    _logger.LogInformation("VietQR credentials validated successfully for username: {Username}", username);
                    _vietQRLogger.LogAuthenticationAttempt(username, true);
                    return Result<bool>.Success(true);
                }
                else
                {
                    _logger.LogWarning("VietQR credential validation failed for username: {Username}", username);
                    _vietQRLogger.LogAuthenticationAttempt(username, false);
                    return Result<bool>.Failure("Invalid credentials", Shared.Enums.ErrorType.Unauthorized);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating VietQR credentials for username: {Username}", username);
                return Result<bool>.Failure("Credential validation failed", Shared.Enums.ErrorType.Unauthorized);
            }
        }

        public async Task<Result<VietQRApiTokenResponse>> GetVietQRApiTokenAsync()
        {
            try
            {
                await _tokenSemaphore.WaitAsync();

                // Check if we have a valid cached token
                if (_cachedApiToken != null && !_cachedApiToken.IsExpired)
                {
                    _logger.LogDebug("Using cached VietQR API token, expires at: {ExpiresAt}", _cachedApiToken.ExpiresAt);
                    return Result<VietQRApiTokenResponse>.Success(_cachedApiToken);
                }

                // Get fresh token
                _logger.LogInformation("Getting fresh VietQR API token");
                return await RefreshVietQRApiTokenAsync();
            }
            finally
            {
                _tokenSemaphore.Release();
            }
        }

        public async Task<Result<VietQRApiTokenResponse>> RefreshVietQRApiTokenAsync()
        {
            try
            {
                _logger.LogInformation("Refreshing VietQR API token from: {BaseUrl}", _vietQRApiBaseUrl);

                // Create Basic Auth header
                var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_validUsername}:{_validPassword}"));

                var request = new HttpRequestMessage(HttpMethod.Post, "/vqr/api/token_generate");
                request.Headers.Add("Authorization", $"Basic {credentials}");
                request.Headers.Add("Content-Type", "application/json");

                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var tokenResponse = JsonSerializer.Deserialize<VietQRApiTokenResponseDto>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (tokenResponse == null)
                    {
                        _logger.LogError("Failed to deserialize VietQR API token response");
                        return Result<VietQRApiTokenResponse>.Failure("Invalid token response format", Shared.Enums.ErrorType.Internal);
                    }

                    var apiToken = new VietQRApiTokenResponse
                    {
                        AccessToken = tokenResponse.AccessToken,
                        TokenType = tokenResponse.TokenType,
                        ExpiresIn = tokenResponse.ExpiresIn,
                        ExpiresAt = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn)
                    };

                    // Cache the token
                    _cachedApiToken = apiToken;

                    _logger.LogInformation("VietQR API token refreshed successfully, expires at: {ExpiresAt}", apiToken.ExpiresAt);
                    _vietQRLogger.LogTokenGenerated(_validUsername, tokenResponse.ExpiresIn);

                    return Result<VietQRApiTokenResponse>.Success(apiToken);
                }
                else
                {
                    _logger.LogError("VietQR API token request failed. Status: {StatusCode}, Response: {Response}",
                        response.StatusCode, responseContent);

                    // Try to parse error response
                    try
                    {
                        var errorResponse = JsonSerializer.Deserialize<VietQRApiTokenErrorResponseDto>(responseContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        return Result<VietQRApiTokenResponse>.Failure(
                            $"VietQR API error: {errorResponse?.Message ?? "Unknown error"}",
                            Shared.Enums.ErrorType.ExternalServiceError);
                    }
                    catch
                    {
                        return Result<VietQRApiTokenResponse>.Failure(
                            $"VietQR API request failed with status {response.StatusCode}",
                            Shared.Enums.ErrorType.ExternalServiceError);
                    }
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                _logger.LogWarning("VietQR API token request timed out after {TimeoutSeconds} seconds", _apiTimeoutSeconds);
                return Result<VietQRApiTokenResponse>.Failure("VietQR API request timeout", Shared.Enums.ErrorType.ExternalServiceError);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP error during VietQR API token request");
                return Result<VietQRApiTokenResponse>.Failure("Network error during VietQR API request", Shared.Enums.ErrorType.ExternalServiceError);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during VietQR API token refresh");
                return Result<VietQRApiTokenResponse>.Failure("Failed to refresh VietQR API token", Shared.Enums.ErrorType.Internal);
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _tokenSemaphore?.Dispose();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
