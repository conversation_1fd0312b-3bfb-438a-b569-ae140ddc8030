using Shared.Enums;
using System.ComponentModel;

namespace Shared.Helper
{
    /// <summary>
    /// Helper class for property renewal operations
    /// </summary>
    public static class PropertyRenewalHelper
    {
        /// <summary>
        /// Dictionary mapping renewal duration to pricing in VND
        /// </summary>
        private static readonly Dictionary<PropertyRenewalDuration, decimal> _pricingMap = new()
        {
            { PropertyRenewalDuration.TenDays, 55000m },
            { PropertyRenewalDuration.ThirtyDays, 150000m },
            { PropertyRenewalDuration.SixtyDays, 300000m },
            { PropertyRenewalDuration.NinetyDays, 450000m }
        };

        /// <summary>
        /// Gets the price for a specific renewal duration
        /// </summary>
        /// <param name="duration">The renewal duration</param>
        /// <returns>The price in VND</returns>
        /// <exception cref="ArgumentException">Thrown when duration is not supported</exception>
        public static decimal GetPriceByDuration(PropertyRenewalDuration duration)
        {
            if (_pricingMap.TryGetValue(duration, out var price))
            {
                return price;
            }
            
            throw new ArgumentException($"Unsupported renewal duration: {duration}", nameof(duration));
        }

        /// <summary>
        /// Gets the price for a specific renewal duration in days
        /// </summary>
        /// <param name="durationInDays">The renewal duration in days</param>
        /// <returns>The price in VND</returns>
        /// <exception cref="ArgumentException">Thrown when duration is not supported</exception>
        public static decimal GetPriceByDurationInDays(int durationInDays)
        {
            var duration = GetDurationEnumFromDays(durationInDays);
            return GetPriceByDuration(duration);
        }

        /// <summary>
        /// Validates if the given duration in days is supported
        /// </summary>
        /// <param name="durationInDays">The duration in days to validate</param>
        /// <returns>True if the duration is supported, false otherwise</returns>
        public static bool IsValidDuration(int durationInDays)
        {
            return Enum.IsDefined(typeof(PropertyRenewalDuration), durationInDays);
        }

        /// <summary>
        /// Gets the PropertyRenewalDuration enum from days value
        /// </summary>
        /// <param name="durationInDays">The duration in days</param>
        /// <returns>The corresponding PropertyRenewalDuration enum</returns>
        /// <exception cref="ArgumentException">Thrown when duration is not supported</exception>
        public static PropertyRenewalDuration GetDurationEnumFromDays(int durationInDays)
        {
            if (Enum.IsDefined(typeof(PropertyRenewalDuration), durationInDays))
            {
                return (PropertyRenewalDuration)durationInDays;
            }
            
            throw new ArgumentException($"Unsupported renewal duration: {durationInDays} days. Supported durations are: {GetSupportedDurationsText()}", nameof(durationInDays));
        }

        /// <summary>
        /// Gets all supported renewal durations
        /// </summary>
        /// <returns>Array of supported PropertyRenewalDuration values</returns>
        public static PropertyRenewalDuration[] GetSupportedDurations()
        {
            return Enum.GetValues<PropertyRenewalDuration>();
        }

        /// <summary>
        /// Gets all supported renewal durations as integer days
        /// </summary>
        /// <returns>Array of supported duration values in days</returns>
        public static int[] GetSupportedDurationDays()
        {
            return Enum.GetValues<PropertyRenewalDuration>().Select(d => (int)d).ToArray();
        }

        /// <summary>
        /// Gets a formatted text of all supported durations for error messages
        /// </summary>
        /// <returns>Comma-separated string of supported durations</returns>
        public static string GetSupportedDurationsText()
        {
            return string.Join(", ", GetSupportedDurationDays().Select(d => $"{d} ngày"));
        }

        /// <summary>
        /// Gets the description of a PropertyRenewalDuration enum value
        /// </summary>
        /// <param name="duration">The duration enum value</param>
        /// <returns>The description text</returns>
        public static string GetDurationDescription(PropertyRenewalDuration duration)
        {
            var field = duration.GetType().GetField(duration.ToString());
            var attribute = field?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                                 .FirstOrDefault() as DescriptionAttribute;
            
            return attribute?.Description ?? duration.ToString();
        }

        /// <summary>
        /// Gets all available renewal options with their prices
        /// </summary>
        /// <returns>Dictionary mapping duration descriptions to prices</returns>
        public static Dictionary<string, decimal> GetAllRenewalOptions()
        {
            return _pricingMap.ToDictionary(
                kvp => GetDurationDescription(kvp.Key),
                kvp => kvp.Value
            );
        }

        /// <summary>
        /// Gets pricing information for display purposes
        /// </summary>
        /// <returns>Dictionary with duration in days as key and formatted price as value</returns>
        public static Dictionary<int, string> GetPricingDisplayInfo()
        {
            return _pricingMap.ToDictionary(
                kvp => (int)kvp.Key,
                kvp => $"{kvp.Value:N0} VND"
            );
        }
    }
}
