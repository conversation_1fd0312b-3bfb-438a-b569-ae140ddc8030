# VietQR Token Security Testing Guide

## Overview

This document provides comprehensive testing procedures to verify that VietQR tokens are properly restricted to only access VietQR endpoints and cannot access other API endpoints in the system.

## Security Implementation

### VietQR Token Restrictions

1. **Separate Authentication Scheme**: VietQR tokens use a dedicated JWT Bearer scheme (`VietQRBearer`)
2. **Custom Authorization Policy**: `VietQRTokenOnly` policy validates VietQR-specific claims
3. **Endpoint Restriction**: VietQR tokens can only access specific VietQR endpoints
4. **Token Identification**: VietQR tokens contain special claims to identify them as payment gateway tokens

### Allowed VietQR Endpoints

VietQR tokens can ONLY access these endpoints:
- `POST /api/vietqr/transaction-sync`
- `GET /api/vietqr/health`
- `POST /api/vietqr/create-payment`

### Restricted Endpoints

VietQR tokens CANNOT access any other endpoints, including:
- User management endpoints (`/api/auth/*`)
- Property endpoints (`/api/property/*`)
- Wallet endpoints (`/api/wallet/*`)
- Any other API endpoints

## Test Scenarios

### Test 1: Generate VietQR Token

First, obtain a VietQR token for testing:

```bash
# Generate VietQR token
VIETQR_TOKEN=$(curl -s -X POST "https://localhost:7057/api/vietqr/token_generate" \
  -H "Authorization: Basic $(echo -n 'yezhome_vietqr_dev_user:yezhome_vietqr_dev_password_123456' | base64)" \
  -H "Content-Type: application/json" | jq -r '.access_token')

echo "VietQR Token: $VIETQR_TOKEN"
```

### Test 2: Verify VietQR Token Can Access Allowed Endpoints

#### Test 2.1: Health Check (Should Work)
```bash
curl -X GET "https://localhost:7057/api/vietqr/health" \
  -H "Authorization: Bearer $VIETQR_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Result**: 200 OK with health status

#### Test 2.2: Transaction Sync (Should Work)
```bash
curl -X POST "https://localhost:7057/api/vietqr/transaction-sync" \
  -H "Authorization: Bearer $VIETQR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "bankaccount": "**********",
    "amount": 100000,
    "transType": "C",
    "content": "Test payment",
    "transactionid": "TEST123456789",
    "transactiontime": *************,
    "referencenumber": "REF123456",
    "orderId": "ORDER123"
  }'
```

**Expected Result**: 200 OK with transaction processing response

#### Test 2.3: Create Payment (Should Work)
```bash
curl -X POST "https://localhost:7057/api/vietqr/create-payment" \
  -H "Authorization: Bearer $VIETQR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "ORDER123",
    "amount": 100000,
    "description": "Test payment",
    "returnUrl": "https://localhost:3000/payment/success",
    "terminalCode": "TERM001",
    "serviceCode": "SVC001"
  }'
```

**Expected Result**: 200 OK with payment creation response

### Test 3: Verify VietQR Token Cannot Access Restricted Endpoints

#### Test 3.1: User Profile (Should Fail)
```bash
curl -X GET "https://localhost:7057/api/auth/me" \
  -H "Authorization: Bearer $VIETQR_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Result**: 401 Unauthorized or 403 Forbidden

#### Test 3.2: Property Listing (Should Fail)
```bash
curl -X GET "https://localhost:7057/api/property" \
  -H "Authorization: Bearer $VIETQR_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Result**: 401 Unauthorized or 403 Forbidden

#### Test 3.3: Wallet Balance (Should Fail)
```bash
curl -X GET "https://localhost:7057/api/wallet/balance" \
  -H "Authorization: Bearer $VIETQR_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Result**: 401 Unauthorized or 403 Forbidden

#### Test 3.4: User Registration (Should Fail)
```bash
curl -X POST "https://localhost:7057/api/auth/register" \
  -H "Authorization: Bearer $VIETQR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123",
    "fullName": "Test User"
  }'
```

**Expected Result**: 401 Unauthorized or 403 Forbidden

### Test 4: Verify Regular JWT Token Cannot Access VietQR Endpoints

First, obtain a regular user JWT token:

```bash
# Login with regular user credentials
REGULAR_TOKEN=$(curl -s -X POST "https://localhost:7057/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "UserPassword123"
  }' | jq -r '.data.token')

echo "Regular Token: $REGULAR_TOKEN"
```

#### Test 4.1: Try to Access VietQR Transaction Sync (Should Fail)
```bash
curl -X POST "https://localhost:7057/api/vietqr/transaction-sync" \
  -H "Authorization: Bearer $REGULAR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "bankaccount": "**********",
    "amount": 100000,
    "transType": "C",
    "content": "Test payment",
    "transactionid": "TEST123456789",
    "transactiontime": *************,
    "referencenumber": "REF123456",
    "orderId": "ORDER123"
  }'
```

**Expected Result**: 401 Unauthorized or 403 Forbidden

#### Test 4.2: Try to Access VietQR Create Payment (Should Fail)
```bash
curl -X POST "https://localhost:7057/api/vietqr/create-payment" \
  -H "Authorization: Bearer $REGULAR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "ORDER123",
    "amount": 100000,
    "description": "Test payment"
  }'
```

**Expected Result**: 401 Unauthorized or 403 Forbidden

## Automated Security Test Script

### PowerShell Script

```powershell
# VietQR Token Security Test Script
$baseUrl = "https://localhost:7057"
$credentials = "yezhome_vietqr_dev_user:yezhome_vietqr_dev_password_123456"
$encodedCredentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes($credentials))

Write-Host "VietQR Token Security Testing..." -ForegroundColor Green

# Step 1: Generate VietQR Token
Write-Host "`n1. Generating VietQR Token..." -ForegroundColor Yellow
try {
    $tokenResponse = Invoke-RestMethod -Uri "$baseUrl/api/vietqr/token_generate" `
        -Method POST `
        -Headers @{
            "Authorization" = "Basic $encodedCredentials"
            "Content-Type" = "application/json"
        }
    
    $vietqrToken = $tokenResponse.access_token
    Write-Host "✓ VietQR token generated successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to generate VietQR token: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Test VietQR Token on Allowed Endpoints
Write-Host "`n2. Testing VietQR Token on Allowed Endpoints..." -ForegroundColor Yellow

# Test Health Check
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/api/vietqr/health" `
        -Method GET `
        -Headers @{ "Authorization" = "Bearer $vietqrToken" }
    Write-Host "✓ VietQR Health Check: ALLOWED (as expected)" -ForegroundColor Green
} catch {
    Write-Host "✗ VietQR Health Check: DENIED (unexpected)" -ForegroundColor Red
}

# Step 3: Test VietQR Token on Restricted Endpoints
Write-Host "`n3. Testing VietQR Token on Restricted Endpoints..." -ForegroundColor Yellow

$restrictedEndpoints = @(
    @{ Method = "GET"; Path = "/api/auth/me"; Name = "User Profile" },
    @{ Method = "GET"; Path = "/api/property"; Name = "Property Listing" },
    @{ Method = "GET"; Path = "/api/wallet/balance"; Name = "Wallet Balance" }
)

foreach ($endpoint in $restrictedEndpoints) {
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl$($endpoint.Path)" `
            -Method $endpoint.Method `
            -Headers @{ "Authorization" = "Bearer $vietqrToken" }
        Write-Host "✗ $($endpoint.Name): ALLOWED (security issue!)" -ForegroundColor Red
    } catch {
        if ($_.Exception.Response.StatusCode -eq 401 -or $_.Exception.Response.StatusCode -eq 403) {
            Write-Host "✓ $($endpoint.Name): DENIED (as expected)" -ForegroundColor Green
        } else {
            Write-Host "? $($endpoint.Name): Unexpected error - $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

Write-Host "`nVietQR Token Security Testing Completed!" -ForegroundColor Green
```

### Bash Script

```bash
#!/bin/bash

# VietQR Token Security Test Script
BASE_URL="https://localhost:7057"
CREDENTIALS="yezhome_vietqr_dev_user:yezhome_vietqr_dev_password_123456"
ENCODED_CREDENTIALS=$(echo -n "$CREDENTIALS" | base64)

echo "VietQR Token Security Testing..."

# Step 1: Generate VietQR Token
echo -e "\n1. Generating VietQR Token..."
TOKEN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/vietqr/token_generate" \
    -H "Authorization: Basic $ENCODED_CREDENTIALS" \
    -H "Content-Type: application/json")

VIETQR_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')

if [ "$VIETQR_TOKEN" != "null" ] && [ "$VIETQR_TOKEN" != "" ]; then
    echo "✓ VietQR token generated successfully"
else
    echo "✗ Failed to generate VietQR token"
    echo "$TOKEN_RESPONSE"
    exit 1
fi

# Step 2: Test VietQR Token on Allowed Endpoints
echo -e "\n2. Testing VietQR Token on Allowed Endpoints..."

# Test Health Check
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" -X GET "$BASE_URL/api/vietqr/health" \
    -H "Authorization: Bearer $VIETQR_TOKEN")

HTTP_CODE="${HEALTH_RESPONSE: -3}"
if [ "$HTTP_CODE" = "200" ]; then
    echo "✓ VietQR Health Check: ALLOWED (as expected)"
else
    echo "✗ VietQR Health Check: DENIED (unexpected) - HTTP $HTTP_CODE"
fi

# Step 3: Test VietQR Token on Restricted Endpoints
echo -e "\n3. Testing VietQR Token on Restricted Endpoints..."

declare -a RESTRICTED_ENDPOINTS=(
    "GET:/api/auth/me:User Profile"
    "GET:/api/property:Property Listing"
    "GET:/api/wallet/balance:Wallet Balance"
)

for endpoint in "${RESTRICTED_ENDPOINTS[@]}"; do
    IFS=':' read -r method path name <<< "$endpoint"
    
    HTTP_CODE=$(curl -s -w "%{http_code}" -o /dev/null -X "$method" "$BASE_URL$path" \
        -H "Authorization: Bearer $VIETQR_TOKEN")
    
    if [ "$HTTP_CODE" = "401" ] || [ "$HTTP_CODE" = "403" ]; then
        echo "✓ $name: DENIED (as expected) - HTTP $HTTP_CODE"
    elif [ "$HTTP_CODE" = "200" ]; then
        echo "✗ $name: ALLOWED (security issue!) - HTTP $HTTP_CODE"
    else
        echo "? $name: Unexpected response - HTTP $HTTP_CODE"
    fi
done

echo -e "\nVietQR Token Security Testing Completed!"
```

## Expected Security Behavior

### Successful Security Implementation

When the security is properly implemented, you should see:

1. **VietQR Token Generation**: ✓ Success
2. **VietQR Health Check**: ✓ Allowed (200 OK)
3. **VietQR Transaction Sync**: ✓ Allowed (200 OK)
4. **VietQR Create Payment**: ✓ Allowed (200 OK)
5. **User Profile Access**: ✓ Denied (401/403)
6. **Property Listing Access**: ✓ Denied (401/403)
7. **Wallet Balance Access**: ✓ Denied (401/403)
8. **Other Endpoints**: ✓ Denied (401/403)

### Security Failure Indicators

If you see any of these, there's a security issue:

- VietQR token can access user endpoints (401/403 expected)
- VietQR token can access property endpoints (401/403 expected)
- VietQR token can access wallet endpoints (401/403 expected)
- Regular JWT token can access VietQR endpoints (401/403 expected)

## Troubleshooting

### Common Issues

1. **VietQR Token Cannot Access VietQR Endpoints**
   - Check VietQR configuration in appsettings.json
   - Verify VietQRBearer authentication scheme is registered
   - Check VietQRTokenOnly policy configuration

2. **VietQR Token Can Access Restricted Endpoints**
   - Verify VietQRTokenHandler is properly registered
   - Check endpoint path validation in IsVietQREndpoint method
   - Ensure authorization policy is correctly applied

3. **Authorization Always Fails**
   - Check token claims (token_type, scope)
   - Verify issuer and audience configuration
   - Check token expiration

### Debug Logging

Enable debug logging to troubleshoot authorization issues:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "RealEstate.API.CustomAuthorizationPolicy.VietQRTokenHandler": "Debug"
    }
  }
}
```
