using System.Net;

namespace RealEstate.InternalAPI.Middleware
{
    /// <summary>
    /// Middleware to restrict access to internal API endpoints to localhost only
    /// </summary>
    public class LocalhostOnlyMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<LocalhostOnlyMiddleware> _logger;

        public LocalhostOnlyMiddleware(RequestDelegate next, ILogger<LocalhostOnlyMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only apply IP restriction to jobs endpoints
            if (!context.Request.Path.StartsWithSegments("/api/internal/jobs"))
            {
                await _next(context);
                return;
            }

            var remoteIpAddress = context.Connection.RemoteIpAddress;
            
            // Check if the request is coming from localhost
            if (!IsLocalhost(remoteIpAddress))
            {
                _logger.LogWarning("Access denied to internal API endpoint {Path} from non-localhost IP: {RemoteIpAddress}", 
                    context.Request.Path, remoteIpAddress);
                
                context.Response.StatusCode = 403;
                await context.Response.WriteAsync("Access denied. Internal API endpoints are only accessible from localhost.");
                return;
            }

            _logger.LogDebug("Localhost access granted for request to {Path} from {RemoteIpAddress}", 
                context.Request.Path, remoteIpAddress);
            
            await _next(context);
        }

        /// <summary>
        /// Check if the IP address is localhost
        /// </summary>
        /// <param name="ipAddress">The IP address to check</param>
        /// <returns>True if the IP is localhost, false otherwise</returns>
        private static bool IsLocalhost(IPAddress? ipAddress)
        {
            if (ipAddress == null)
                return false;

            // Check for IPv4 localhost (127.0.0.1)
            if (ipAddress.Equals(IPAddress.Loopback))
                return true;

            // Check for IPv6 localhost (::1)
            if (ipAddress.Equals(IPAddress.IPv6Loopback))
                return true;

            // Check for mapped IPv4 localhost in IPv6 format
            if (ipAddress.IsIPv4MappedToIPv6)
            {
                var mappedIPv4 = ipAddress.MapToIPv4();
                if (mappedIPv4.Equals(IPAddress.Loopback))
                    return true;
            }

            // Additional check for local addresses that might be considered localhost
            // This includes 127.x.x.x range
            if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
            {
                var bytes = ipAddress.GetAddressBytes();
                if (bytes[0] == 127) // 127.x.x.x range
                    return true;
            }

            return false;
        }
    }

    /// <summary>
    /// Extension method to register the LocalhostOnlyMiddleware
    /// </summary>
    public static class LocalhostOnlyMiddlewareExtensions
    {
        public static IApplicationBuilder UseLocalhostOnly(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<LocalhostOnlyMiddleware>();
        }
    }
}
