﻿using Amazon.Runtime.Internal;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using Shared.Enums;
using Shared.Responses;
using Shared.Results;
using System.Security.Claims;

namespace RealEstate.API.Controllers
{
    /// <summary>
    /// Controller for authentication and user-related operations.
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : BaseController
    {
        private readonly IAuthService _authService;
        private readonly ITokenService _tokenService;
        private readonly IUserService _userService;
        private readonly INotificationService _notificationService;
        private readonly IPasswordResetService _passwordResetService;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;

        public AuthController(IAuthService authService,
            ILogger<AuthController> logger,
            ITokenService tokenService,
            IUserService userService,
            INotificationService notificationService,
            IPasswordResetService passwordResetService,
            IConfiguration configuration)
        {
            _authService = authService;
            _tokenService = tokenService;
            _userService = userService;
            _notificationService = notificationService;
            _passwordResetService = passwordResetService;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Retrieves the profile of the authenticated user.
        /// </summary>
        /// <returns>A ProfileDto containing user details, member rank information, and highlight fee.</returns>
        [Authorize]
        [HttpGet("me")]
        public async Task<IActionResult> GetUserProfile()
        {
            var userIdString = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userIdString == null)
            {
                var unauthorizedResponse = ApiResponse<object>.Failure("Unauthorized: User ID not found in token.");
                return Unauthorized(unauthorizedResponse);
            }

            var parsedUserId = Guid.Parse(userIdString);

            // Chỉ cần gọi một hàm duy nhất từ service
            var result = await _userService.GetUserProfileAsync(parsedUserId);

            return HandleResult(result);
        }

        /// <summary>
        /// Registers a new user.
        /// </summary>
        /// <param name="registerDto">The registration data.</param>
        /// <returns>A UserDto upon successful registration.</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register(CreateUserDto registerDto)
        {
            var result = await _authService.RegisterAsync(registerDto);

            if (result.IsSuccess)
            {
                var user = result.Value;
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var request = new NotificationRequest
                        {
                            // Chỉ định rõ chỉ gửi qua kênh Email
                            TargetChannels = NotificationChannel.Email,
                            RecipientId = user.Id,
                            RecipientEmail = user.Email,
                            EmailType = EmailType.Welcome.ToString(),
                            Title = "Chào mừng thành viên mới",
                            Data = new Dictionary<string, string>
                            {
                                { "user_name", user.FullName }
                            }
                        };

                        // Gửi đi
                        await _notificationService.SendAsync(request);
                    }
                    catch (Exception ex)
                    {
                        // Rất quan trọng: Luôn log lỗi trong tác vụ fire and forget
                        _logger.LogError(ex, "Lỗi khi gửi email chào mừng tới {Email}", user.Email);
                    }
                });
            }

            // Nếu thành công, dữ liệu chắc chắn có trong result.Value
            return HandleResult(result);
        }

        /// <summary>
        /// Authenticates a user and provides a JWT token along with a refresh token.
        /// </summary>
        /// <param name="loginDto">The login credentials.</param>
        /// <returns>A UserDto containing user details and a JWT token.</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login(LoginDto loginDto)
        {
            var result = await _authService.LoginAsync(loginDto);
            var refreshToken = _tokenService.GenerateRefreshToken();

            // Store Refresh Token in HttpOnly Secure Cookies
            Response.Cookies.Append("refreshToken", refreshToken, new CookieOptions
            {
                HttpOnly = true,
                Secure = true, // Enable on HTTPS
                SameSite = SameSiteMode.Strict,
                Expires = DateTime.UtcNow.AddDays(7)
            });

            return HandleResult(result);
        }

        /// <summary>
        /// Initiates the password reset process.
        /// </summary>
        /// <param name="resetPasswordDto">The password reset data.</param>
        /// <returns>A message indicating the status of the password reset.</returns>
        [HttpPost("forgot-password")]
        [AllowAnonymous]
        public async Task<IActionResult> ForgotPassword(ForgotPasswordDto resetPasswordDto)
        {
            var result = await _userService.GetUserByEmailAsync(resetPasswordDto.GetNormalizedEmail());
            if (result.IsFailure || result.Value == null)
            {
                return HandleResult(result);
            }

            var user = result.Value;

            var resetTokenResult = await _passwordResetService.GenerateAndStoreResetToken(user.Id);
            string resetToken = resetTokenResult.Value;

            // Lấy URL của Next.js Frontend từ cấu hình
            var nextJsBaseUrl = _configuration["NextJsBaseUrl"];
            if (string.IsNullOrEmpty(nextJsBaseUrl))
            {
                return HandleResult(Result.Failure("Next.js base URL is not configured.", ErrorType.NotFound));
            }

            var callbackUrl = $"{nextJsBaseUrl}/reset-password?userId={user.Id}&token={resetToken}";

            _logger.LogInformation($"Password reset callback URL: {callbackUrl}");

            _ = Task.Run(async () =>
            {
                try
                {
                    var request = new NotificationRequest
                    {
                        // Chỉ định rõ chỉ gửi qua kênh Email
                        TargetChannels = NotificationChannel.Email,
                        RecipientId = user.Id,
                        RecipientEmail = user.Email,
                        EmailType = EmailType.ForgotPassword.ToString(),
                        Title = "Đặt Lại Mật Khẩu",
                        Data = new Dictionary<string, string>
                        {
                            { "reset_password_url", callbackUrl },
                            { "user_name", user.FullName  },
                            { "reset_password_timeout", "60" } // Thời gian hết hạn 60 phút
                        }, 
                    };

                    // Gửi đi
                    await _notificationService.SendAsync(request);
                    _logger.LogInformation("Email sent for password reset to {Email}", resetPasswordDto.GetNormalizedEmail());
                }
                catch (Exception ex)
                {
                    // Rất quan trọng: Luôn log lỗi trong tác vụ fire and forget
                    _logger.LogError(ex, "Lỗi khi gửi email reset password tới {Email}", resetPasswordDto.GetNormalizedEmail());
                }
            });

            return NoContent();
        }

        [HttpPost("reset-password")]
        [AllowAnonymous]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordDto model)
        {
            // 1. Xác minh token
            var validateResult = await _passwordResetService.ValidateResetToken(model.Token);

            if (validateResult.IsFailure)
            {
                return HandleResult(validateResult);
            }

            var (isValid, userId) = validateResult.Value;
            // Đảm bảo userId từ token khớp với userId trong request (đề phòng giả mạo)
            if (userId != model.UserId)
            {
                return HandleResult(Result.Failure("Token không hợp lệ", Shared.Enums.ErrorType.Validation));
            }

            // 2. Tìm người dùng và cập nhật mật khẩu (logic của bạn)
            var userResult = await _userService.GetUserByIdAsync(userId);
            if (userResult.IsFailure)
            {
                return HandleResult(userResult);
            }

            var resetResult = await _userService.UpdateUserPassword(userId, model.NewPassword, model.Token);

            return HandleResult(resetResult);
        }

        /// <summary>
        /// Changes the password of the authenticated user.
        /// </summary>
        /// <param name="changePasswordDto">The data for changing the password.</param>
        /// <returns>A UserDto upon successful password change.</returns>
        [Authorize]
        [HttpPatch("/api/[controller]/me/password")]
        public async Task<IActionResult> Password(ChangePasswordDto changePasswordDto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _authService.ChangePassword(changePasswordDto);
            return HandleResult(result);
        }

        /// <summary>
        /// Validates the current authentication token.
        /// </summary>
        /// <returns>A message indicating whether the token is valid.</returns>
        [HttpGet("validate-token")]
        [Authorize]
        public async Task<IActionResult> ValidateToken()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            // TODO: need to implement check the token is retrieve or not
            return Ok();
        }
    }
}
