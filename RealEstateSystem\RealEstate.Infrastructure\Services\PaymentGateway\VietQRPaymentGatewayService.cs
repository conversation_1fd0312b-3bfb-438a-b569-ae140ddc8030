using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RealEstate.Application.DTO.VietQR;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Interfaces;
using Shared.Results;
using Shared.Enums;
using System.Text;
using System.Text.Json;
using System.Security.Cryptography;
using System.Net.Http.Json;

namespace RealEstate.Infrastructure.Services.PaymentGateway
{
    /// <summary>
    /// VietQR payment gateway service implementation with comprehensive logging
    /// Includes real QR code generation via VietQR API
    /// </summary>
    public class VietQRPaymentGatewayService : IVietQRPaymentGatewayService
    {
        private readonly IConfiguration _config;
        private readonly ILogger<VietQRPaymentGatewayService> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IWalletService _walletService;
        private readonly IVietQRTokenService _vietQRTokenService;
        private readonly HttpClient _httpClient;
        private readonly VietQRConfigurationDto _vietQRConfig;
        private readonly string _vietQRApiBaseUrl;
        private readonly int _apiTimeoutSeconds;

        public VietQRPaymentGatewayService(
            IConfiguration config,
            ILogger<VietQRPaymentGatewayService> logger,
            IUnitOfWork unitOfWork,
            IWalletService walletService,
            IVietQRTokenService vietQRTokenService,
            HttpClient httpClient)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _walletService = walletService ?? throw new ArgumentNullException(nameof(walletService));
            _vietQRTokenService = vietQRTokenService ?? throw new ArgumentNullException(nameof(vietQRTokenService));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));

            // Load VietQR configuration
            _vietQRConfig = new VietQRConfigurationDto
            {
                SecretKey = _config["VietQR:SecretKey"] ?? string.Empty,
                Username = _config["VietQR:Username"] ?? string.Empty,
                Password = _config["VietQR:Password"] ?? string.Empty,
                Issuer = _config["VietQR:Issuer"] ?? "VietQR-PaymentGateway",
                Audience = _config["VietQR:Audience"] ?? "VietQR-Client",
                TokenExpirationMinutes = _config.GetValue<int>("VietQR:TokenExpirationMinutes", 5),
                BankAccount = _config["VietQR:BankAccount"] ?? string.Empty,
                BankCode = _config["VietQR:BankCode"] ?? string.Empty,
                MerchantName = _config["VietQR:MerchantName"] ?? "YezHome",
                IsEnabled = _config.GetValue<bool>("VietQR:IsEnabled", false)
            };

            // VietQR API configuration
            var isProduction = _config.GetValue<bool>("VietQR:IsProduction", false);
            _vietQRApiBaseUrl = isProduction
                ? _config["VietQR:Api:BaseUrlProd"] ?? "https://api.vietqr.org"
                : _config["VietQR:Api:BaseUrlDev"] ?? "https://dev.vietqr.org";

            _apiTimeoutSeconds = _config.GetValue<int>("VietQR:Api:TimeoutSeconds", 30);

            // Configure HTTP client
            _httpClient.BaseAddress = new Uri(_vietQRApiBaseUrl);
            _httpClient.Timeout = TimeSpan.FromSeconds(_apiTimeoutSeconds);

            _logger.LogInformation("VietQRPaymentGatewayService initialized. Enabled: {IsEnabled}, Bank: {BankCode}, API: {ApiUrl}",
                _vietQRConfig.IsEnabled, _vietQRConfig.BankCode, _vietQRApiBaseUrl);
        }

        public async Task<Result<VietQRPaymentResponseDto>> CreatePaymentRequestAsync(VietQRPaymentRequestDto request)
        {
            try
            {
                _logger.LogInformation("Creating VietQR payment request for OrderId: {OrderId}, Amount: {Amount}", 
                    request.OrderId, request.Amount);

                if (!_vietQRConfig.IsEnabled)
                {
                    _logger.LogWarning("VietQR payment gateway is disabled");
                    return Result<VietQRPaymentResponseDto>.Failure("VietQR payment gateway is not enabled", ErrorType.Validation);
                }

                // Validate request
                var validationResult = ValidatePaymentRequest(request);
                if (!validationResult.IsSuccess)
                {
                    _logger.LogWarning("VietQR payment request validation failed for OrderId: {OrderId}. Error: {Error}",
                        request.OrderId, validationResult.ErrorMessage);
                    return Result<VietQRPaymentResponseDto>.Failure(validationResult.ErrorMessage ?? "Validation failed", ErrorType.Validation);
                }

                // Generate transaction reference
                var transactionReference = GenerateTransactionReference(request.OrderId);

                // Generate QR code content
                var qrCodeResult = await GenerateQRCodeAsync(request.Amount, request.Description, request.OrderId);
                if (!qrCodeResult.IsSuccess)
                {
                    _logger.LogError("Failed to generate QR code for OrderId: {OrderId}. Error: {Error}",
                        request.OrderId, qrCodeResult.ErrorMessage);
                    return Result<VietQRPaymentResponseDto>.Failure("Failed to generate QR code", ErrorType.Internal);
                }

                var response = new VietQRPaymentResponseDto
                {
                    IsSuccess = true,
                    QRCode = qrCodeResult.Value,
                    TransactionReference = transactionReference,
                    PaymentUrl = null // VietQR typically uses QR codes, not redirect URLs
                };

                _logger.LogInformation("VietQR payment request created successfully for OrderId: {OrderId}, TransactionRef: {TransactionRef}", 
                    request.OrderId, transactionReference);

                return Result<VietQRPaymentResponseDto>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating VietQR payment request for OrderId: {OrderId}", request.OrderId);
                return Result<VietQRPaymentResponseDto>.Failure("Failed to create payment request", ErrorType.Internal);
            }
        }

        public async Task<Result<VietQRTransactionSyncSuccessResponseDto>> ProcessTransactionSyncAsync(VietQRTransactionSyncRequestDto transactionData)
        {
            try
            {
                _logger.LogInformation("Processing VietQR transaction sync for TransactionId: {TransactionId}, OrderId: {OrderId}, Amount: {Amount}", 
                    transactionData.TransactionId, transactionData.OrderId, transactionData.Amount);

                // Validate transaction data
                var validationResult = ValidateTransactionData(transactionData);
                if (!validationResult.IsSuccess)
                {
                    _logger.LogWarning("VietQR transaction validation failed for TransactionId: {TransactionId}. Error: {Error}",
                        transactionData.TransactionId, validationResult.ErrorMessage);

                    return Result<VietQRTransactionSyncSuccessResponseDto>.Failure(validationResult.ErrorMessage ?? "Validation failed", ErrorType.Validation);
                }

                // Validate signature if present
                if (!string.IsNullOrEmpty(transactionData.Sign))
                {
                    var signatureValidation = await ValidateTransactionSignatureAsync(transactionData);
                    if (!signatureValidation.IsSuccess || !signatureValidation.Value)
                    {
                        _logger.LogWarning("VietQR transaction signature validation failed for TransactionId: {TransactionId}", 
                            transactionData.TransactionId);
                        return Result<VietQRTransactionSyncSuccessResponseDto>.Failure("Invalid transaction signature", ErrorType.Validation);
                    }
                }

                // Convert to internal processing DTO
                var processDto = ConvertToProcessDto(transactionData);

                // Process the transaction through wallet service
                var walletResult = await ProcessWalletTransaction(processDto);
                if (!walletResult.IsSuccess)
                {
                    _logger.LogError("Wallet transaction processing failed for TransactionId: {TransactionId}. Error: {Error}",
                        transactionData.TransactionId, walletResult.ErrorMessage);

                    return Result<VietQRTransactionSyncSuccessResponseDto>.Failure(walletResult.ErrorMessage ?? "Wallet processing failed", ErrorType.Internal);
                }

                // Generate reference transaction ID
                var refTransactionId = GenerateReferenceTransactionId(transactionData.TransactionId);

                var response = new VietQRTransactionSyncSuccessResponseDto
                {
                    Error = false,
                    ErrorReason = null,
                    ToastMessage = "Transaction processed successfully",
                    Object = new VietQRTransactionResponseObjectDto
                    {
                        RefTransactionId = refTransactionId
                    }
                };

                _logger.LogInformation("VietQR transaction processed successfully. TransactionId: {TransactionId}, RefTransactionId: {RefTransactionId}", 
                    transactionData.TransactionId, refTransactionId);

                return Result<VietQRTransactionSyncSuccessResponseDto>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing VietQR transaction sync for TransactionId: {TransactionId}", 
                    transactionData.TransactionId);
                return Result<VietQRTransactionSyncSuccessResponseDto>.Failure("Transaction processing failed", ErrorType.Internal);
            }
        }

        public async Task<Result<bool>> ValidateTransactionSignatureAsync(VietQRTransactionSyncRequestDto transactionData)
        {
            try
            {
                _logger.LogDebug("Validating VietQR transaction signature for TransactionId: {TransactionId}", 
                    transactionData.TransactionId);

                if (string.IsNullOrEmpty(transactionData.Sign))
                {
                    _logger.LogDebug("No signature provided for TransactionId: {TransactionId}", transactionData.TransactionId);
                    return Result<bool>.Success(true); // No signature to validate
                }

                // Create signature string from transaction data
                var signatureString = CreateSignatureString(transactionData);
                var expectedSignature = GenerateSignature(signatureString, _vietQRConfig.SecretKey);

                bool isValid = expectedSignature.Equals(transactionData.Sign, StringComparison.OrdinalIgnoreCase);

                if (isValid)
                {
                    _logger.LogDebug("VietQR transaction signature validated successfully for TransactionId: {TransactionId}", 
                        transactionData.TransactionId);
                }
                else
                {
                    _logger.LogWarning("VietQR transaction signature validation failed for TransactionId: {TransactionId}", 
                        transactionData.TransactionId);
                }

                return Result<bool>.Success(isValid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating VietQR transaction signature for TransactionId: {TransactionId}", 
                    transactionData.TransactionId);
                return Result<bool>.Failure("Signature validation failed", ErrorType.Internal);
            }
        }

        public async Task<Result<string>> GenerateQRCodeAsync(long amount, string content, string orderId)
        {
            try
            {
                _logger.LogDebug("Generating VietQR QR code for OrderId: {OrderId}, Amount: {Amount}", orderId, amount);

                if (!_vietQRConfig.IsEnabled)
                {
                    _logger.LogWarning("VietQR is disabled, generating mock QR code");
                    // Fallback to mock QR code when VietQR is disabled
                    var qrContent = $"{_vietQRConfig.BankCode}|{_vietQRConfig.BankAccount}|{amount}|{content}|{orderId}";
                    var qrCodeData = Convert.ToBase64String(Encoding.UTF8.GetBytes(qrContent));
                    return Result<string>.Success(qrCodeData);
                }

                // Generate real QR code via VietQR API
                var generateRequest = new VietQRGenerateCodeRequestDto
                {
                    BankCode = _vietQRConfig.BankCode,
                    BankAccount = _vietQRConfig.BankAccount,
                    UserBankName = _vietQRConfig.MerchantName,
                    Content = TruncateContent(content, 23), // VietQR max 23 characters
                    QrType = (int)VietQRType.Dynamic, // Dynamic QR with amount
                    Amount = amount,
                    OrderId = TruncateContent(orderId, 13), // VietQR max 13 characters
                    TransType = "C" // Credit transaction
                };

                var qrResult = await GenerateVietQRCodeAsync(generateRequest);
                if (!qrResult.IsSuccess)
                {
                    _logger.LogError("Failed to generate VietQR code for OrderId: {OrderId}. Error: {Error}",
                        orderId, qrResult.ErrorMessage);
                    return Result<string>.Failure(qrResult.ErrorMessage ?? "Failed to generate QR code", ErrorType.ExternalServiceError);
                }

                _logger.LogDebug("VietQR QR code generated successfully for OrderId: {OrderId}", orderId);
                return Result<string>.Success(qrResult?.Value?.QrCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating VietQR QR code for OrderId: {OrderId}", orderId);
                return Result<string>.Failure("Failed to generate QR code", ErrorType.Internal);
            }
        }

        /// <summary>
        /// Generates QR code via VietQR API
        /// </summary>
        public async Task<Result<VietQRGenerateCodeResponseDto>> GenerateVietQRCodeAsync(VietQRGenerateCodeRequestDto request)
        {
            try
            {
                _logger.LogInformation("Calling VietQR API to generate QR code for OrderId: {OrderId}", request.OrderId);

                // Get valid API token
                var tokenResult = await _vietQRTokenService.GetVietQRApiTokenAsync();
                if (!tokenResult.IsSuccess)
                {
                    _logger.LogError("Failed to get VietQR API token: {Error}", tokenResult.ErrorMessage);
                    return Result<VietQRGenerateCodeResponseDto>.Failure("Failed to authenticate with VietQR API", ErrorType.ExternalServiceError);
                }

                // Prepare API request
                var httpRequest = new HttpRequestMessage(HttpMethod.Post, "/vqr/api/qr/generate-customer");
                httpRequest.Headers.Add("Authorization", $"Bearer {tokenResult?.Value?.AccessToken}");
                httpRequest.Content = JsonContent.Create(request, options: new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                // Call VietQR API
                var response = await _httpClient.SendAsync(httpRequest);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var qrResponse = JsonSerializer.Deserialize<VietQRGenerateCodeResponseDto>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (qrResponse == null)
                    {
                        _logger.LogError("Failed to deserialize VietQR generate code response");
                        return Result<VietQRGenerateCodeResponseDto>.Failure("Invalid QR response format", ErrorType.Internal);
                    }

                    _logger.LogInformation("VietQR code generated successfully. OrderId: {OrderId}, TransactionRefId: {TransactionRefId}",
                        request.OrderId, qrResponse.TransactionRefId);

                    return Result<VietQRGenerateCodeResponseDto>.Success(qrResponse);
                }
                else
                {
                    _logger.LogError("VietQR generate code request failed. Status: {StatusCode}, Response: {Response}",
                        response.StatusCode, responseContent);

                    // Try to parse error response
                    try
                    {
                        var errorResponse = JsonSerializer.Deserialize<VietQRGenerateCodeErrorResponseDto>(responseContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        return Result<VietQRGenerateCodeResponseDto>.Failure(
                            $"VietQR API error: {errorResponse?.Message ?? "Unknown error"}",
                            ErrorType.ExternalServiceError);
                    }
                    catch
                    {
                        return Result<VietQRGenerateCodeResponseDto>.Failure(
                            $"VietQR API request failed with status {response.StatusCode}",
                            ErrorType.ExternalServiceError);
                    }
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                _logger.LogWarning("VietQR generate code request timed out after {TimeoutSeconds} seconds", _apiTimeoutSeconds);
                return Result<VietQRGenerateCodeResponseDto>.Failure("VietQR API request timeout", ErrorType.ExternalServiceError);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP error during VietQR generate code request");
                return Result<VietQRGenerateCodeResponseDto>.Failure("Network error during VietQR API request", ErrorType.ExternalServiceError);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during VietQR code generation");
                return Result<VietQRGenerateCodeResponseDto>.Failure("Failed to generate VietQR code", ErrorType.Internal);
            }
        }

        #region Private Helper Methods

        private Result<bool> ValidatePaymentRequest(VietQRPaymentRequestDto request)
        {
            if (string.IsNullOrEmpty(request.OrderId))
                return Result<bool>.Failure("OrderId is required", ErrorType.Validation);

            if (request.Amount < 1000)
                return Result<bool>.Failure("Amount must be at least 1000 VND", ErrorType.Validation);

            if (string.IsNullOrEmpty(request.Description))
                return Result<bool>.Failure("Description is required", ErrorType.Validation);

            return Result<bool>.Success(true);
        }

        private Result<bool> ValidateTransactionData(VietQRTransactionSyncRequestDto transactionData)
        {
            if (string.IsNullOrEmpty(transactionData.TransactionId))
                return Result<bool>.Failure("TransactionId is required", ErrorType.Validation);

            if (string.IsNullOrEmpty(transactionData.OrderId))
                return Result<bool>.Failure("OrderId is required", ErrorType.Validation);

            if (transactionData.Amount <= 0)
                return Result<bool>.Failure("Amount must be greater than 0", ErrorType.Validation);

            if (string.IsNullOrEmpty(transactionData.BankAccount))
                return Result<bool>.Failure("BankAccount is required", ErrorType.Validation);

            return Result<bool>.Success(true);
        }

        private VietQRTransactionProcessDto ConvertToProcessDto(VietQRTransactionSyncRequestDto transactionData)
        {
            return new VietQRTransactionProcessDto
            {
                TransactionId = transactionData.TransactionId,
                OrderId = transactionData.OrderId,
                Amount = transactionData.Amount,
                BankAccount = transactionData.BankAccount,
                Content = transactionData.Content,
                TransactionTime = DateTimeOffset.FromUnixTimeMilliseconds(transactionData.TransactionTime).DateTime,
                ReferenceNumber = transactionData.ReferenceNumber,
                TransType = transactionData.TransType,
                TerminalCode = transactionData.TerminalCode,
                SubTerminalCode = transactionData.SubTerminalCode,
                ServiceCode = transactionData.ServiceCode,
                UrlLink = transactionData.UrlLink,
                Sign = transactionData.Sign
            };
        }

        private async Task<Result<bool>> ProcessWalletTransaction(VietQRTransactionProcessDto processDto)
        {
            try
            {
                // Here you would integrate with your existing wallet service
                // This is a placeholder implementation
                _logger.LogInformation("Processing wallet transaction for OrderId: {OrderId}", processDto.OrderId);
                
                // TODO: Implement actual wallet transaction processing
                // Example: await _walletService.ProcessVietQRPayment(processDto);
                
                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing wallet transaction for OrderId: {OrderId}", processDto.OrderId);
                return Result<bool>.Failure("Wallet transaction processing failed", ErrorType.Internal);
            }
        }

        private string GenerateTransactionReference(string orderId)
        {
            return $"VIETQR_{orderId}_{DateTime.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid().ToString("N")[..8]}";
        }

        private string GenerateReferenceTransactionId(string transactionId)
        {
            return $"REF_{transactionId}_{DateTime.UtcNow:yyyyMMddHHmmss}";
        }

        private string CreateSignatureString(VietQRTransactionSyncRequestDto transactionData)
        {
            // Create signature string based on VietQR specification
            return $"{transactionData.TransactionId}|{transactionData.Amount}|{transactionData.BankAccount}|{transactionData.Content}|{transactionData.TransactionTime}";
        }

        private string GenerateSignature(string data, string secretKey)
        {
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            return Convert.ToBase64String(hash);
        }

        private static string TruncateContent(string content, int maxLength)
        {
            if (string.IsNullOrEmpty(content))
                return string.Empty;

            if (content.Length <= maxLength)
                return content;

            return content.Substring(0, maxLength);
        }

        #endregion
    }
}
