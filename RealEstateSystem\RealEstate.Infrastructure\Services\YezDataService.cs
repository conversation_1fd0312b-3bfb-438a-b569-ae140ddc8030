using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RealEstate.Application.Services
{
    public class YezDataService : IYezDataService
    {
        private readonly IUnitOfWork _unitOfWork;

        public YezDataService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<HighlightFeeDto>> GetAllHighlightFeesAsync()
        {
            var highlightFees = await _unitOfWork.HighlightFees.GetQueryable()
                .Select(hf => new HighlightFeeDto
                {
                    RankName = hf.RankName,
                    Fee = hf.Fee
                })
                .ToListAsync();

            return highlightFees;
        }

        public async Task<HighlightFeeDto> GetHighlightFeeByRankAsync(string rankName)
        {
            var highlightFee = await _unitOfWork.HighlightFees.GetQueryable()
                .FirstOrDefaultAsync(hf => hf.RankName == rankName);

            if (highlightFee == null)
            {
                return null;
            }

            return new HighlightFeeDto
            {
                RankName = highlightFee.RankName,
                Fee = highlightFee.Fee
            };
        }

        public async Task<IEnumerable<MemberRankListDto>> GetAllMemberRankingsAsync()
        {
            // Get all member rankings with their highlight fees
            var memberRankings = await _unitOfWork.MemberRankings.GetQueryable()
                .Include(mr => mr.HighlightFee)
                .OrderBy(mr => mr.MinSpent)
                .ToListAsync();

            // Map to DTOs
            var result = memberRankings.Select(mr => new MemberRankListDto
            {
                RankName = mr.RankName,
                MinSpent = mr.MinSpent,
                MaxSpent = mr.MaxSpent,
                HighlightFee = mr.HighlightFee?.Fee ?? 0
            }).ToList();

            return result;
        }

        public async Task<MemberRankListDto> GetMemberRankingByNameAsync(string rankName)
        {
            // Get the member ranking with its highlight fee
            var memberRanking = await _unitOfWork.MemberRankings.GetQueryable()
                .Include(mr => mr.HighlightFee)
                .FirstOrDefaultAsync(mr => mr.RankName == rankName);

            if (memberRanking == null)
            {
                return null;
            }

            return new MemberRankListDto
            {
                RankName = memberRanking.RankName,
                MinSpent = memberRanking.MinSpent,
                MaxSpent = memberRanking.MaxSpent,
                HighlightFee = memberRanking.HighlightFee?.Fee ?? 0
            };
        }

        public async Task<IEnumerable<ListingPriceDto>> GetAllListingPricesAsync()
        {
            var listingPrices = await _unitOfWork.ListingPrices.GetQueryable()
                .Where(lp => !lp.IsDeleted)
                .OrderBy(lp => lp.Days)
                .Select(lp => new ListingPriceDto
                {
                    Id = lp.Id,
                    Days = lp.Days,
                    Price = lp.Price,
                    IsDefault = lp.IsDefault
                })
                .ToListAsync();

            return listingPrices;
        }

        public async Task<ListingPriceDto> GetDefaultListingPriceAsync()
        {
            var defaultListingPrice = await _unitOfWork.ListingPrices.GetQueryable()
                .Where(lp => !lp.IsDeleted && lp.IsDefault)
                .FirstOrDefaultAsync();

            if (defaultListingPrice == null)
            {
                return null;
            }

            return new ListingPriceDto
            {
                Id = defaultListingPrice.Id,
                Days = defaultListingPrice.Days,
                Price = defaultListingPrice.Price,
                IsDefault = defaultListingPrice.IsDefault
            };
        }

        public async Task<ListingPriceDto> GetListingPriceByDaysAsync(int days)
        {
            var listingPrice = await _unitOfWork.ListingPrices.GetQueryable()
                .Where(lp => !lp.IsDeleted && lp.Days == days)
                .FirstOrDefaultAsync();

            if (listingPrice == null)
            {
                return null;
            }

            return new ListingPriceDto
            {
                Id = listingPrice.Id,
                Days = listingPrice.Days,
                Price = listingPrice.Price,
                IsDefault = listingPrice.IsDefault
            };
        }
    }
}
