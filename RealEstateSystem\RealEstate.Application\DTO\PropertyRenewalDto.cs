using System.ComponentModel.DataAnnotations;
using Shared.Helper;

namespace RealEstate.Application.DTO
{
    public class PropertyRenewalDto : IValidatableObject
    {
        [Required]
        public Guid PropertyId { get; set; }

        [Required]
        public int DurationInDays { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (!PropertyRenewalHelper.IsValidDuration(DurationInDays))
            {
                yield return new ValidationResult(
                    $"Thời gian gia hạn không hợp lệ. <PERSON><PERSON><PERSON> tùy chọn hợp lệ: {PropertyRenewalHelper.GetSupportedDurationsText()}",
                    new[] { nameof(DurationInDays) });
            }
        }
    }
}
