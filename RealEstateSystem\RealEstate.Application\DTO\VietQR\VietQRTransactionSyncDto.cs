using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace RealEstate.Application.DTO.VietQR
{
    /// <summary>
    /// Request DTO for VietQR transaction sync endpoint
    /// This represents the transaction data that VietQR will send to our callback endpoint
    /// </summary>
    public class VietQRTransactionSyncRequestDto
    {
        [Required]
        [JsonPropertyName("bankaccount")]
        public string BankAccount { get; set; } = string.Empty;

        [Required]
        [JsonPropertyName("amount")]
        public long Amount { get; set; }

        [Required]
        [JsonPropertyName("transType")]
        public string TransType { get; set; } = string.Empty; // D/C (Debit/Credit)

        [Required]
        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        [Required]
        [JsonPropertyName("transactionid")]
        public string TransactionId { get; set; } = string.Empty;

        [Required]
        [JsonPropertyName("transactiontime")]
        public long TransactionTime { get; set; } // Timestamp in milliseconds

        [Required]
        [JsonPropertyName("referencenumber")]
        public string ReferenceNumber { get; set; } = string.Empty;

        [Required]
        [JsonPropertyName("orderId")]
        public string OrderId { get; set; } = string.Empty;

        [JsonPropertyName("terminalCode")]
        public string? TerminalCode { get; set; }

        [JsonPropertyName("subTerminalCode")]
        public string? SubTerminalCode { get; set; }

        [JsonPropertyName("serviceCode")]
        public string? ServiceCode { get; set; }

        [JsonPropertyName("urlLink")]
        public string? UrlLink { get; set; }

        [JsonPropertyName("sign")]
        public string? Sign { get; set; }
    }

    /// <summary>
    /// Success response DTO for VietQR transaction sync endpoint
    /// </summary>
    public class VietQRTransactionSyncSuccessResponseDto
    {
        [JsonPropertyName("error")]
        public bool Error { get; set; } = false;

        [JsonPropertyName("errorReason")]
        public string? ErrorReason { get; set; }

        [JsonPropertyName("toastMessage")]
        public string ToastMessage { get; set; } = string.Empty;

        [JsonPropertyName("object")]
        public VietQRTransactionResponseObjectDto Object { get; set; } = new();
    }

    /// <summary>
    /// Error response DTO for VietQR transaction sync endpoint
    /// </summary>
    public class VietQRTransactionSyncErrorResponseDto
    {
        [JsonPropertyName("error")]
        public bool Error { get; set; } = true;

        [JsonPropertyName("errorReason")]
        public string ErrorReason { get; set; } = string.Empty;

        [JsonPropertyName("toastMessage")]
        public string ToastMessage { get; set; } = string.Empty;

        [JsonPropertyName("object")]
        public object? Object { get; set; } = null;
    }

    /// <summary>
    /// Transaction response object containing the reference transaction ID
    /// </summary>
    public class VietQRTransactionResponseObjectDto
    {
        [JsonPropertyName("reftransactionid")]
        public string RefTransactionId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Internal DTO for processing VietQR transaction data
    /// </summary>
    public class VietQRTransactionProcessDto
    {
        public string TransactionId { get; set; } = string.Empty;
        public string OrderId { get; set; } = string.Empty;
        public long Amount { get; set; }
        public string BankAccount { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime TransactionTime { get; set; }
        public string ReferenceNumber { get; set; } = string.Empty;
        public string TransType { get; set; } = string.Empty;
        public string? TerminalCode { get; set; }
        public string? SubTerminalCode { get; set; }
        public string? ServiceCode { get; set; }
        public string? UrlLink { get; set; }
        public string? Sign { get; set; }
    }
}
