using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.InternalAPI.Controllers
{
    /// <summary>
    /// Controller for managing system settings
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class SettingController : BaseController
    {
        private readonly ISettingService _settingService;

        public SettingController(ISettingService settingService)
        {
            _settingService = settingService;
        }

        /// <summary>
        /// Get all system settings
        /// </summary>
        /// <returns>List of all settings</returns>
        /// <response code="200">Returns the list of settings</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetSettings()
        {
            var result = await _settingService.GetAllAsync();
            return HandleResult(result);
        }

        /// <summary>
        /// Get a specific setting by key
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <returns>The setting value</returns>
        /// <response code="200">Returns the setting</response>
        /// <response code="404">If the setting is not found</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("{key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetSettingByKey(string key)
        {
            var result = await _settingService.GetByKeyAsync(key);
            return HandleResult(result);
        }

        /// <summary>
        /// Update a specific setting
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="value">The new setting value</param>
        /// <returns>Success or failure result</returns>
        /// <response code="204">Setting updated successfully</response>
        /// <response code="404">If the setting is not found</response>
        /// <response code="400">If the request is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpPut("{key}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdateSetting(string key, [FromBody] string value)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                return BadRequest("Setting value cannot be empty");
            }

            var result = await _settingService.UpdateAsync(key, value);
            return HandleResult(result);
        }

        /// <summary>
        /// Update multiple settings in bulk
        /// </summary>
        /// <param name="request">The bulk update request containing multiple settings</param>
        /// <returns>Success or failure result</returns>
        /// <response code="204">Settings updated successfully</response>
        /// <response code="400">If the request is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpPut("bulk")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdateSettingsBulk([FromBody] SettingBulkUpdateRequest request)
        {
            if (request?.Items == null || !request.Items.Any())
            {
                return BadRequest("No settings provided for update");
            }

            var result = await _settingService.BulkUpdateAsync(request);
            return HandleResult(result);
        }

        /// <summary>
        /// Create a new setting
        /// </summary>
        /// <param name="request">The setting creation request</param>
        /// <returns>Success or failure result</returns>
        /// <response code="201">Setting created successfully</response>
        /// <response code="400">If the request is invalid or setting already exists</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CreateSetting([FromBody] SettingCreateRequest request)
        {
            if (string.IsNullOrWhiteSpace(request?.Key) || string.IsNullOrWhiteSpace(request?.Value))
            {
                return BadRequest("Setting key and value are required");
            }

            var result = await _settingService.CreateAsync(request);
            if (result.IsSuccess)
            {
                return CreatedAtAction(nameof(GetSettingByKey), new { key = request.Key }, null);
            }
            
            return HandleResult(result);
        }
    }
}
