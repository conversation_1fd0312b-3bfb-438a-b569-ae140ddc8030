﻿using System.Diagnostics;
using System.Security.Claims;

namespace RealEstate.API.Middleware
{
    public class LoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<LoggingMiddleware> _logger;

        public LoggingMiddleware(RequestDelegate next, ILogger<LoggingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var sw = Stopwatch.StartNew();
            var traceId = context.TraceIdentifier;
            var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";

            // Bắt đầu một scope để các log bên trong đều có chung TraceId và UserId
            using (_logger.BeginScope("Request {TraceId} from IP {IpAddress}", traceId, ipAddress))
            {
                // Log thời điểm bắt đầu request
                _logger.LogInformation(
                    "Starting {RequestMethod} {RequestPath}",
                    context.Request.Method,
                    context.Request.Path);

                // Chuyển request đi tiếp trong pipeline (đây là lúc controller và service chạy)
                await _next(context);

                sw.Stop();

                var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "anonymous";

                // Log thời điểm kết thúc request
                // Status code lúc này đã được xác định (bởi Controller hoặc bởi ExceptionMiddleware)
                _logger.LogInformation(
                    "Finished HTTP {RequestMethod} {RequestPath}  for User {UserId} with status {StatusCode} in {DurationMs}ms.",
                    context.Request.Method,
                    context.Request.Path,
                    userId,
                    context.Response.StatusCode,
                    sw.ElapsedMilliseconds);
            }
        }
    }
}
