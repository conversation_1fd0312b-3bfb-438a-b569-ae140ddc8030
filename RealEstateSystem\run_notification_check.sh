#!/bin/bash

# =============================================================================
# YezHome Property Expiration Notification Check Script
# =============================================================================
# This script calls the internal API endpoint to check for properties that are
# expiring and sends appropriate notifications to property owners.
#
# Usage: ./run_notification_check.sh
# 
# Requirements:
# - curl must be installed
# - The YezHome Internal API must be running on localhost
# - The API key must match the one configured in appsettings.json
# =============================================================================

# Configuration
API_URL="http://localhost:8080/api/internal/jobs/check-expirations"
API_KEY="YezHome_Internal_API_Secret_Key_2024_v1.0_SuperSecure_DoNotShare"
LOG_FILE="/var/log/yezhome/notification_check.log"
TIMEOUT=30

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"

# Function to log messages with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to check if curl is available
check_curl() {
    if ! command -v curl &> /dev/null; then
        log_message "ERROR: curl is not installed. Please install curl to run this script."
        exit 1
    fi
}

# Function to check if API is reachable
check_api_health() {
    local health_url="http://localhost:8080/healthz"
    if ! curl -s --connect-timeout 5 "$health_url" > /dev/null; then
        log_message "WARNING: API health check failed. The API might not be running."
        return 1
    fi
    return 0
}

# Main function to call the notification check API
run_notification_check() {
    log_message "Starting property expiration notification check..."
    
    # Make the API call
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" \
        --connect-timeout "$TIMEOUT" \
        --max-time "$TIMEOUT" \
        -X POST \
        -H "X-Internal-Api-Key: $API_KEY" \
        -H "Content-Type: application/json" \
        "$API_URL" 2>&1)
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all lines except last)
    response_body=$(echo "$response" | head -n -1)
    
    # Check the HTTP status code
    case $http_code in
        200)
            log_message "SUCCESS: Notification check completed successfully"
            log_message "Response: $response_body"
            return 0
            ;;
        401)
            log_message "ERROR: Unauthorized - Invalid API key"
            return 1
            ;;
        403)
            log_message "ERROR: Forbidden - Access denied (not from localhost or invalid API key)"
            return 1
            ;;
        404)
            log_message "ERROR: Not Found - API endpoint not found"
            return 1
            ;;
        500)
            log_message "ERROR: Internal Server Error"
            log_message "Response: $response_body"
            return 1
            ;;
        000)
            log_message "ERROR: Could not connect to API server"
            return 1
            ;;
        *)
            log_message "ERROR: Unexpected HTTP status code: $http_code"
            log_message "Response: $response_body"
            return 1
            ;;
    esac
}

# Main execution
main() {
    log_message "=== YezHome Notification Check Script Started ==="
    
    # Check prerequisites
    check_curl
    
    # Optional: Check API health (don't fail if health check fails)
    if ! check_api_health; then
        log_message "Proceeding despite health check failure..."
    fi
    
    # Run the notification check
    if run_notification_check; then
        log_message "=== Notification check completed successfully ==="
        exit 0
    else
        log_message "=== Notification check failed ==="
        exit 1
    fi
}

# Execute main function
main "$@"
