# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release 
WORKDIR /src

# Copy project files in dependency order
COPY ["RealEstate.InternalAPI/RealEstate.InternalAPI.csproj", "RealEstate.InternalAPI/"]
COPY ["RealEstate.Infrastructure/RealEstate.Infrastructure.csproj", "RealEstate.Infrastructure/"]
COPY ["RealEstate.Application/RealEstate.Application.csproj", "RealEstate.Application/"]
COPY ["RealEstate.Domain/RealEstate.Domain.csproj", "RealEstate.Domain/"]
COPY ["Shared/Shared.csproj", "Shared/"]

# Restore dependencies
RUN dotnet restore "./RealEstate.InternalAPI/RealEstate.InternalAPI.csproj"

# Copy all source code
COPY . .

# Build the application
WORKDIR "/src/RealEstate.InternalAPI"
RUN dotnet build "./RealEstate.InternalAPI.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./RealEstate.InternalAPI.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app

# Create directories for file storage (if using local storage)
RUN mkdir -p /app/Templates

COPY --from=publish /app/publish .

# Copy Templates files to the Templates directory
COPY ["RealEstate.InternalAPI/Templates/", "/app/Templates/"]

ENTRYPOINT ["dotnet", "RealEstate.InternalAPI.dll"]