# VietQR Token Security Implementation

## Overview

This document describes the implementation of security restrictions for VietQR tokens, ensuring they can only access VietQR-specific endpoints and cannot access other API endpoints in the system.

## Security Architecture

### Multi-Scheme Authentication

The system now uses two separate JWT Bearer authentication schemes:

1. **Default JWT Bearer** (`JwtBearerDefaults.AuthenticationScheme`)
   - Used for regular user authentication
   - Uses `JWT:Key` from configuration
   - Can access all user-authorized endpoints

2. **VietQR Bearer** (`VietQRBearer`)
   - Used exclusively for VietQR payment gateway authentication
   - Uses `VietQR:SecretKey` from configuration
   - Restricted to VietQR endpoints only

### Authorization Policy

**Policy Name**: `VietQRTokenOnly`

**Purpose**: Restricts VietQR tokens to only access VietQR endpoints

**Components**:
- Uses `VietQRBearer` authentication scheme
- Validates VietQR-specific token claims
- Enforces endpoint access restrictions

## Implementation Details

### 1. VietQR Token Claims

VietQR tokens contain special claims to identify them:

```csharp
var claims = new List<Claim>
{
    new Claim(JwtRegisteredClaimNames.Sub, username),
    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
    new Claim(JwtRegisteredClaimNames.Iat, 
        new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString(), 
        ClaimValueTypes.Integer64),
    new Claim("token_type", "vietqr_payment_gateway"), // Identifies as VietQR token
    new Claim("scope", "vietqr_endpoints_only") // Scope limitation
};
```

### 2. Authorization Requirement

**File**: `RealEstate.API/CustomAuthorizationPolicy/VietQRTokenRequirement.cs`

```csharp
public class VietQRTokenRequirement : IAuthorizationRequirement
{
    public string AllowedAudience { get; }
    public string AllowedIssuer { get; }
    
    public VietQRTokenRequirement(string allowedIssuer, string allowedAudience)
    {
        AllowedIssuer = allowedIssuer;
        AllowedAudience = allowedAudience;
    }
}
```

### 3. Authorization Handler

**File**: `RealEstate.API/CustomAuthorizationPolicy/VietQRTokenHandler.cs`

**Key Validations**:
1. **Authentication Check**: Ensures user is authenticated
2. **Token Extraction**: Extracts Bearer token from Authorization header
3. **Token Validation**: Validates token using VietQRTokenService
4. **Issuer/Audience Validation**: Validates against VietQR configuration
5. **Token Type Validation**: Ensures token has `token_type: "vietqr_payment_gateway"`
6. **Endpoint Restriction**: Validates request is to an allowed VietQR endpoint

**Allowed VietQR Endpoints**:
- `/api/vietqr/transaction-sync`
- `/api/vietqr/health`
- `/api/vietqr/create-payment`

### 4. Authentication Configuration

**File**: `RealEstate.API/Program.cs`

```csharp
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    // Default JWT Bearer for regular user authentication
    .AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options => { ... })
    // VietQR JWT Bearer for VietQR payment gateway authentication
    .AddJwtBearer("VietQRBearer", options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["VietQR:Issuer"],
            ValidAudience = builder.Configuration["VietQR:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["VietQR:SecretKey"])),
            ClockSkew = TimeSpan.Zero
        };
    });
```

### 5. Authorization Policy Configuration

```csharp
builder.Services.AddAuthorization(options =>
{
    // VietQR token authorization policy
    options.AddPolicy("VietQRTokenOnly", policy =>
    {
        policy.AuthenticationSchemes.Add("VietQRBearer");
        policy.Requirements.Add(new VietQRTokenRequirement(
            builder.Configuration["VietQR:Issuer"] ?? "YezHome-VietQR-Gateway",
            builder.Configuration["VietQR:Audience"] ?? "VietQR-Client"));
    });
});
```

### 6. Controller Endpoint Protection

**File**: `RealEstate.API/Controllers/VietQRController.cs`

```csharp
[HttpPost("transaction-sync")]
[Authorize(Policy = "VietQRTokenOnly", AuthenticationSchemes = "VietQRBearer")]
public async Task<IActionResult> TransactionSync([FromBody] VietQRTransactionSyncRequestDto transactionData)

[HttpPost("create-payment")]
[Authorize(Policy = "VietQRTokenOnly", AuthenticationSchemes = "VietQRBearer")]
public async Task<IActionResult> CreatePayment([FromBody] VietQRPaymentRequestDto request)
```

## Security Benefits

### 1. Token Isolation
- VietQR tokens cannot access user management endpoints
- Regular user tokens cannot access VietQR payment endpoints
- Complete separation of authentication contexts

### 2. Endpoint Restriction
- VietQR tokens are restricted to specific payment-related endpoints
- Prevents unauthorized access to sensitive user data
- Reduces attack surface for payment gateway integration

### 3. Token Identification
- Special claims identify VietQR tokens
- Prevents token substitution attacks
- Clear audit trail for payment operations

### 4. Configuration Separation
- Separate secret keys for different token types
- Independent token expiration settings
- Isolated issuer/audience validation

## Security Validation

### What VietQR Tokens CAN Access:
✅ `POST /api/vietqr/transaction-sync` - Transaction notifications from VietQR
✅ `GET /api/vietqr/health` - Health check endpoint
✅ `POST /api/vietqr/create-payment` - Payment request creation

### What VietQR Tokens CANNOT Access:
❌ `GET /api/auth/me` - User profile
❌ `GET /api/property` - Property listings
❌ `GET /api/wallet/balance` - Wallet operations
❌ Any other API endpoints outside VietQR scope

### What Regular JWT Tokens CANNOT Access:
❌ `POST /api/vietqr/transaction-sync` - VietQR transaction sync
❌ `POST /api/vietqr/create-payment` - VietQR payment creation

## Error Responses

### Invalid VietQR Token on Restricted Endpoint
```json
{
  "type": "https://tools.ietf.org/html/rfc7235#section-3.1",
  "title": "Unauthorized",
  "status": 401,
  "detail": "VietQR token can only access VietQR endpoints"
}
```

### Regular Token on VietQR Endpoint
```json
{
  "type": "https://tools.ietf.org/html/rfc7235#section-3.1",
  "title": "Unauthorized", 
  "status": 401,
  "detail": "Token is not a VietQR payment gateway token"
}
```

## Logging and Monitoring

### Security Events Logged:
- VietQR token authorization attempts
- Endpoint access violations
- Token validation failures
- Authentication scheme mismatches

### Log Examples:
```
[INFO] VietQR token authorization successful for path: /api/vietqr/transaction-sync from IP: *************
[WARN] VietQR authorization failed: VietQR token cannot access non-VietQR endpoint. Path: /api/auth/me
[WARN] VietQR authorization failed: Token is not a VietQR payment gateway token. TokenType: user_authentication
```

## Configuration Requirements

### appsettings.json
```json
{
  "VietQR": {
    "SecretKey": "separate_256_bit_key_for_vietqr_tokens",
    "Issuer": "YezHome-VietQR-Gateway",
    "Audience": "VietQR-Client",
    "TokenExpirationMinutes": 5
  }
}
```

### Key Security Notes:
1. **VietQR:SecretKey** must be different from **JWT:Key**
2. **VietQR:Issuer** and **VietQR:Audience** should be VietQR-specific
3. Short token expiration (5 minutes) for payment security

## Testing

Use the comprehensive test suite in `VietQR_Token_Security_Test.md` to verify:
1. VietQR tokens can access allowed endpoints
2. VietQR tokens cannot access restricted endpoints  
3. Regular tokens cannot access VietQR endpoints
4. Proper error responses for unauthorized access

## Maintenance

### Regular Security Checks:
1. Verify endpoint access restrictions
2. Monitor authorization logs for violations
3. Test token isolation periodically
4. Review and update allowed endpoint list as needed

### Security Updates:
1. Rotate VietQR secret keys regularly
2. Update token expiration policies
3. Review and audit authorization logic
4. Monitor for new security vulnerabilities
