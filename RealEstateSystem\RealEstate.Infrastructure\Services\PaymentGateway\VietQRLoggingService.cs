using Microsoft.Extensions.Logging;
using RealEstate.Application.DTO.VietQR;
using System.Text.Json;

namespace RealEstate.Infrastructure.Services.PaymentGateway
{
    /// <summary>
    /// Specialized logging service for VietQR payment gateway operations
    /// Provides structured logging for payment tracking, debugging, and audit trails
    /// </summary>
    public class VietQRLoggingService
    {
        private readonly ILogger<VietQRLoggingService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public VietQRLoggingService(ILogger<VietQRLoggingService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        /// <summary>
        /// Logs payment request creation with detailed information
        /// </summary>
        public void LogPaymentRequestCreated(VietQRPaymentRequestDto request, string transactionReference, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_PaymentRequest_Created",
                OrderId = request.OrderId,
                Amount = request.Amount,
                Description = request.Description,
                TransactionReference = transactionReference,
                TerminalCode = request.TerminalCode,
                ServiceCode = request.ServiceCode,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("VietQR Payment Request Created: {PaymentData}", 
                JsonSerializer.Serialize(logData, _jsonOptions));
        }

        /// <summary>
        /// Logs payment request creation failure
        /// </summary>
        public void LogPaymentRequestFailed(VietQRPaymentRequestDto request, string error, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_PaymentRequest_Failed",
                OrderId = request.OrderId,
                Amount = request.Amount,
                Error = error,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogWarning("VietQR Payment Request Failed: {PaymentData}", 
                JsonSerializer.Serialize(logData, _jsonOptions));
        }

        /// <summary>
        /// Logs transaction sync received from VietQR
        /// </summary>
        public void LogTransactionSyncReceived(VietQRTransactionSyncRequestDto transaction, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_TransactionSync_Received",
                TransactionId = transaction.TransactionId,
                OrderId = transaction.OrderId,
                Amount = transaction.Amount,
                BankAccount = transaction.BankAccount,
                TransType = transaction.TransType,
                Content = transaction.Content,
                ReferenceNumber = transaction.ReferenceNumber,
                TransactionTime = transaction.TransactionTime,
                TerminalCode = transaction.TerminalCode,
                SubTerminalCode = transaction.SubTerminalCode,
                ServiceCode = transaction.ServiceCode,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("VietQR Transaction Sync Received: {TransactionData}", 
                JsonSerializer.Serialize(logData, _jsonOptions));
        }

        /// <summary>
        /// Logs successful transaction processing
        /// </summary>
        public void LogTransactionProcessed(VietQRTransactionSyncRequestDto transaction, string refTransactionId, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_Transaction_Processed",
                TransactionId = transaction.TransactionId,
                OrderId = transaction.OrderId,
                Amount = transaction.Amount,
                RefTransactionId = refTransactionId,
                ProcessingTime = DateTime.UtcNow,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("VietQR Transaction Processed Successfully: {TransactionData}", 
                JsonSerializer.Serialize(logData, _jsonOptions));
        }

        /// <summary>
        /// Logs transaction processing failure
        /// </summary>
        public void LogTransactionProcessingFailed(VietQRTransactionSyncRequestDto transaction, string error, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_Transaction_ProcessingFailed",
                TransactionId = transaction.TransactionId,
                OrderId = transaction.OrderId,
                Amount = transaction.Amount,
                Error = error,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogError("VietQR Transaction Processing Failed: {TransactionData}", 
                JsonSerializer.Serialize(logData, _jsonOptions));
        }

        /// <summary>
        /// Logs token generation events
        /// </summary>
        public void LogTokenGenerated(string username, int expiresIn, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_Token_Generated",
                Username = username,
                ExpiresIn = expiresIn,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("VietQR Token Generated: {TokenData}", 
                JsonSerializer.Serialize(logData, _jsonOptions));
        }

        /// <summary>
        /// Logs token validation events
        /// </summary>
        public void LogTokenValidation(bool isValid, string? reason = null, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_Token_Validation",
                IsValid = isValid,
                Reason = reason,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            if (isValid)
            {
                _logger.LogDebug("VietQR Token Validation: {TokenData}", 
                    JsonSerializer.Serialize(logData, _jsonOptions));
            }
            else
            {
                _logger.LogWarning("VietQR Token Validation Failed: {TokenData}", 
                    JsonSerializer.Serialize(logData, _jsonOptions));
            }
        }

        /// <summary>
        /// Logs authentication attempts
        /// </summary>
        public void LogAuthenticationAttempt(string username, bool isSuccessful, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_Authentication_Attempt",
                Username = username,
                IsSuccessful = isSuccessful,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            if (isSuccessful)
            {
                _logger.LogInformation("VietQR Authentication Successful: {AuthData}", 
                    JsonSerializer.Serialize(logData, _jsonOptions));
            }
            else
            {
                _logger.LogWarning("VietQR Authentication Failed: {AuthData}", 
                    JsonSerializer.Serialize(logData, _jsonOptions));
            }
        }

        /// <summary>
        /// Logs signature validation events
        /// </summary>
        public void LogSignatureValidation(string transactionId, bool isValid, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_Signature_Validation",
                TransactionId = transactionId,
                IsValid = isValid,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            if (isValid)
            {
                _logger.LogDebug("VietQR Signature Validation Successful: {SignatureData}", 
                    JsonSerializer.Serialize(logData, _jsonOptions));
            }
            else
            {
                _logger.LogWarning("VietQR Signature Validation Failed: {SignatureData}", 
                    JsonSerializer.Serialize(logData, _jsonOptions));
            }
        }

        /// <summary>
        /// Logs QR code generation events
        /// </summary>
        public void LogQRCodeGenerated(string orderId, long amount, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_QRCode_Generated",
                OrderId = orderId,
                Amount = amount,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogDebug("VietQR QR Code Generated: {QRData}", 
                JsonSerializer.Serialize(logData, _jsonOptions));
        }

        /// <summary>
        /// Logs system errors and exceptions
        /// </summary>
        public void LogSystemError(string operation, Exception exception, string? additionalContext = null, string? ipAddress = null)
        {
            var logData = new
            {
                EventType = "VietQR_System_Error",
                Operation = operation,
                ErrorMessage = exception.Message,
                StackTrace = exception.StackTrace,
                AdditionalContext = additionalContext,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogError(exception, "VietQR System Error in {Operation}: {ErrorData}", 
                operation, JsonSerializer.Serialize(logData, _jsonOptions));
        }

        /// <summary>
        /// Logs security events (suspicious activities, rate limiting, etc.)
        /// </summary>
        public void LogSecurityEvent(string eventType, string description, string? ipAddress = null, string? additionalData = null)
        {
            var logData = new
            {
                EventType = $"VietQR_Security_{eventType}",
                Description = description,
                AdditionalData = additionalData,
                ClientIP = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogWarning("VietQR Security Event: {SecurityData}", 
                JsonSerializer.Serialize(logData, _jsonOptions));
        }

        /// <summary>
        /// Logs performance metrics
        /// </summary>
        public void LogPerformanceMetric(string operation, TimeSpan duration, bool isSuccessful, string? additionalData = null)
        {
            var logData = new
            {
                EventType = "VietQR_Performance_Metric",
                Operation = operation,
                DurationMs = duration.TotalMilliseconds,
                IsSuccessful = isSuccessful,
                AdditionalData = additionalData,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("VietQR Performance Metric: {PerformanceData}", 
                JsonSerializer.Serialize(logData, _jsonOptions));
        }
    }
}
